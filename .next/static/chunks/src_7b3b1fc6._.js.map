{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border border-border/50 py-6 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:-translate-y-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wLACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/dashboard/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';\nimport { TrendingUp, TrendingDown, Users, DollarSign, BookOpen, Calendar } from 'lucide-react';\n\n// Mock analytics data\nconst attendanceData = [\n  { month: 'Jan', percentage: 92 },\n  { month: 'Feb', percentage: 89 },\n  { month: 'Mar', percentage: 94 },\n  { month: 'Apr', percentage: 91 },\n  { month: 'May', percentage: 88 },\n  { month: 'Jun', percentage: 93 },\n];\n\nconst gradeDistribution = [\n  { grade: 'A+', count: 45, color: '#10B981' },\n  { grade: 'A', count: 78, color: '#34D399' },\n  { grade: 'B+', count: 92, color: '#60A5FA' },\n  { grade: 'B', count: 67, color: '#A78BFA' },\n  { grade: 'C+', count: 34, color: '#F59E0B' },\n  { grade: 'C', count: 23, color: '#EF4444' },\n  { grade: 'D', count: 12, color: '#6B7280' },\n  { grade: 'F', count: 5, color: '#374151' },\n];\n\nconst feeCollectionData = [\n  { month: 'Jan', collected: 45000, pending: 5000 },\n  { month: 'Feb', collected: 48000, pending: 3000 },\n  { month: 'Mar', collected: 52000, pending: 4000 },\n  { month: 'Apr', collected: 49000, pending: 6000 },\n  { month: 'May', collected: 51000, pending: 2000 },\n  { month: 'Jun', collected: 54000, pending: 3500 },\n];\n\nconst classPerformance = [\n  { class: 'Grade 1-A', average: 85 },\n  { class: 'Grade 1-B', average: 82 },\n  { class: 'Grade 2-A', average: 88 },\n  { class: 'Grade 2-B', average: 84 },\n  { class: 'Grade 3-A', average: 90 },\n  { class: 'Grade 3-B', average: 87 },\n];\n\nexport default function AnalyticsPage() {\n  const [selectedPeriod, setSelectedPeriod] = useState('6months');\n\n  const totalStudents = 356;\n  const totalRevenue = 298500;\n  const averageAttendance = 91.2;\n  const totalBooks = 1250;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Analytics Dashboard</h1>\n          <p className=\"text-gray-600\">Visual insights and performance metrics</p>\n        </div>\n        <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>\n          <SelectTrigger className=\"w-40\">\n            <SelectValue placeholder=\"Select period\" />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"1month\">Last Month</SelectItem>\n            <SelectItem value=\"3months\">Last 3 Months</SelectItem>\n            <SelectItem value=\"6months\">Last 6 Months</SelectItem>\n            <SelectItem value=\"1year\">Last Year</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Students</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{totalStudents}</p>\n                <div className=\"flex items-center mt-1\">\n                  <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n                  <span className=\"text-sm text-green-600\">+5.2% from last month</span>\n                </div>\n              </div>\n              <Users className=\"h-8 w-8 text-blue-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Revenue</p>\n                <p className=\"text-2xl font-bold text-gray-900\">₹{totalRevenue.toLocaleString()}</p>\n                <div className=\"flex items-center mt-1\">\n                  <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n                  <span className=\"text-sm text-green-600\">+8.1% from last month</span>\n                </div>\n              </div>\n              <DollarSign className=\"h-8 w-8 text-green-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Avg. Attendance</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{averageAttendance}%</p>\n                <div className=\"flex items-center mt-1\">\n                  <TrendingDown className=\"h-4 w-4 text-red-500 mr-1\" />\n                  <span className=\"text-sm text-red-600\">-1.3% from last month</span>\n                </div>\n              </div>\n              <Calendar className=\"h-8 w-8 text-yellow-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Library Books</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{totalBooks}</p>\n                <div className=\"flex items-center mt-1\">\n                  <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n                  <span className=\"text-sm text-green-600\">+12 new books</span>\n                </div>\n              </div>\n              <BookOpen className=\"h-8 w-8 text-purple-600\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Attendance Trend */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Attendance Trend</CardTitle>\n            <CardDescription>Monthly attendance percentage over time</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={attendanceData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis domain={[80, 100]} />\n                <Tooltip formatter={(value) => [`${value}%`, 'Attendance']} />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"percentage\" \n                  stroke=\"#3B82F6\" \n                  strokeWidth={2}\n                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n\n        {/* Grade Distribution */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Grade Distribution</CardTitle>\n            <CardDescription>Student performance across different grades</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={gradeDistribution}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={100}\n                  dataKey=\"count\"\n                  label={({ grade, count }) => `${grade}: ${count}`}\n                >\n                  {gradeDistribution.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n\n        {/* Fee Collection */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Fee Collection</CardTitle>\n            <CardDescription>Monthly fee collection vs pending amounts</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={feeCollectionData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => [`₹${value.toLocaleString()}`, '']} />\n                <Bar dataKey=\"collected\" fill=\"#10B981\" name=\"Collected\" />\n                <Bar dataKey=\"pending\" fill=\"#EF4444\" name=\"Pending\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n\n        {/* Class Performance */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Class Performance</CardTitle>\n            <CardDescription>Average performance by class</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={classPerformance} layout=\"horizontal\">\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis type=\"number\" domain={[0, 100]} />\n                <YAxis dataKey=\"class\" type=\"category\" width={80} />\n                <Tooltip formatter={(value) => [`${value}%`, 'Average Score']} />\n                <Bar dataKey=\"average\" fill=\"#8B5CF6\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Top Performing Class</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-green-600\">Grade 3-A</p>\n              <p className=\"text-gray-600\">90% Average Score</p>\n              <p className=\"text-sm text-gray-500 mt-2\">Consistently high performance across all subjects</p>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Attendance Leader</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-blue-600\">Grade 2-A</p>\n              <p className=\"text-gray-600\">96% Attendance</p>\n              <p className=\"text-sm text-gray-500 mt-2\">Highest attendance rate this month</p>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Fee Collection Rate</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-purple-600\">94.2%</p>\n              <p className=\"text-gray-600\">Collection Efficiency</p>\n              <p className=\"text-sm text-gray-500 mt-2\">Above target for this quarter</p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQA,sBAAsB;AACtB,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAO,YAAY;IAAG;IAC/B;QAAE,OAAO;QAAO,YAAY;IAAG;IAC/B;QAAE,OAAO;QAAO,YAAY;IAAG;IAC/B;QAAE,OAAO;QAAO,YAAY;IAAG;IAC/B;QAAE,OAAO;QAAO,YAAY;IAAG;IAC/B;QAAE,OAAO;QAAO,YAAY;IAAG;CAChC;AAED,MAAM,oBAAoB;IACxB;QAAE,OAAO;QAAM,OAAO;QAAI,OAAO;IAAU;IAC3C;QAAE,OAAO;QAAK,OAAO;QAAI,OAAO;IAAU;IAC1C;QAAE,OAAO;QAAM,OAAO;QAAI,OAAO;IAAU;IAC3C;QAAE,OAAO;QAAK,OAAO;QAAI,OAAO;IAAU;IAC1C;QAAE,OAAO;QAAM,OAAO;QAAI,OAAO;IAAU;IAC3C;QAAE,OAAO;QAAK,OAAO;QAAI,OAAO;IAAU;IAC1C;QAAE,OAAO;QAAK,OAAO;QAAI,OAAO;IAAU;IAC1C;QAAE,OAAO;QAAK,OAAO;QAAG,OAAO;IAAU;CAC1C;AAED,MAAM,oBAAoB;IACxB;QAAE,OAAO;QAAO,WAAW;QAAO,SAAS;IAAK;IAChD;QAAE,OAAO;QAAO,WAAW;QAAO,SAAS;IAAK;IAChD;QAAE,OAAO;QAAO,WAAW;QAAO,SAAS;IAAK;IAChD;QAAE,OAAO;QAAO,WAAW;QAAO,SAAS;IAAK;IAChD;QAAE,OAAO;QAAO,WAAW;QAAO,SAAS;IAAK;IAChD;QAAE,OAAO;QAAO,WAAW;QAAO,SAAS;IAAK;CACjD;AAED,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAa,SAAS;IAAG;IAClC;QAAE,OAAO;QAAa,SAAS;IAAG;IAClC;QAAE,OAAO;QAAa,SAAS;IAAG;IAClC;QAAE,OAAO;QAAa,SAAS;IAAG;IAClC;QAAE,OAAO;QAAa,SAAS;IAAG;IAClC;QAAE,OAAO;QAAa,SAAS;IAAG;CACnC;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,gBAAgB;IACtB,MAAM,eAAe;IACrB,MAAM,oBAAoB;IAC1B,MAAM,aAAa;IAEnB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAgB,eAAe;;0CAC5C,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;kDAG7C,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAmC;oDAAE,aAAa,cAAc;;;;;;;0DAC7E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;kDAG7C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAoC;oDAAkB;;;;;;;0DACnE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;;;;;;;kDAG3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;kDAG7C,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wCAAC,MAAM;;0DACf,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;gDAAC,QAAQ;oDAAC;oDAAI;iDAAI;;;;;;0DACxB,6LAAC,0JAAA,CAAA,UAAO;gDAAC,WAAW,CAAC,QAAU;wDAAC,GAAG,MAAM,CAAC,CAAC;wDAAE;qDAAa;;;;;;0DAC1D,6LAAC,uJAAA,CAAA,OAAI;gDACH,MAAK;gDACL,SAAQ;gDACR,QAAO;gDACP,aAAa;gDACb,KAAK;oDAAE,MAAM;oDAAW,aAAa;oDAAG,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;0DACP,6LAAC,kJAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,SAAQ;gDACR,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,GAAK,GAAG,MAAM,EAAE,EAAE,OAAO;0DAEhD,kBAAkB,GAAG,CAAC,CAAC,OAAO,sBAC7B,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,KAAK;uDAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0DAG9B,6LAAC,0JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM;;0DACd,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;gDAAC,WAAW,CAAC,QAAU;wDAAC,CAAC,CAAC,EAAE,MAAM,cAAc,IAAI;wDAAE;qDAAG;;;;;;0DACjE,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAY,MAAK;gDAAU,MAAK;;;;;;0DAC7C,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAU,MAAK;gDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAkB,QAAO;;0DACvC,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,MAAK;gDAAS,QAAQ;oDAAC;oDAAG;iDAAI;;;;;;0DACrC,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAQ,MAAK;gDAAW,OAAO;;;;;;0DAC9C,6LAAC,0JAAA,CAAA,UAAO;gDAAC,WAAW,CAAC,QAAU;wDAAC,GAAG,MAAM,CAAC,CAAC;wDAAE;qDAAgB;;;;;;0DAC7D,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAKhD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAKhD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;;;;;;0CAEjC,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD;GAzOwB;KAAA", "debugId": null}}]}