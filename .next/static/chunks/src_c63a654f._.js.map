{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border border-border/50 py-6 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:-translate-y-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wLACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ai/FaceRecognitionAttendance.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Camera, CameraOff, Users, CheckCircle, XCircle, Loader2 } from 'lucide-react';\nimport { toast } from 'sonner';\n\ninterface DetectedStudent {\n  id: string;\n  name: string;\n  confidence: number;\n  timestamp: Date;\n  status: 'present' | 'absent';\n}\n\ninterface FaceRecognitionAttendanceProps {\n  classId?: string;\n  onAttendanceMarked?: (students: DetectedStudent[]) => void;\n}\n\nexport function FaceRecognitionAttendance({ \n  classId, \n  onAttendanceMarked \n}: FaceRecognitionAttendanceProps) {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isCameraActive, setIsCameraActive] = useState(false);\n  const [detectedStudents, setDetectedStudents] = useState<DetectedStudent[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [stream, setStream] = useState<MediaStream | null>(null);\n\n  // Mock student database for demo\n  const mockStudentDatabase = [\n    { id: 'STU001', name: 'John Doe', faceDescriptor: 'mock_descriptor_1' },\n    { id: 'STU002', name: 'Jane Smith', faceDescriptor: 'mock_descriptor_2' },\n    { id: 'STU003', name: 'Mike Johnson', faceDescriptor: 'mock_descriptor_3' },\n    { id: 'STU004', name: 'Sarah Wilson', faceDescriptor: 'mock_descriptor_4' },\n    { id: 'STU005', name: 'David Brown', faceDescriptor: 'mock_descriptor_5' },\n  ];\n\n  const startCamera = async () => {\n    try {\n      setIsLoading(true);\n      const mediaStream = await navigator.mediaDevices.getUserMedia({\n        video: { \n          width: 640, \n          height: 480,\n          facingMode: 'user'\n        }\n      });\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = mediaStream;\n        setStream(mediaStream);\n        setIsCameraActive(true);\n        toast.success('Camera started successfully');\n      }\n    } catch (error) {\n      console.error('Error accessing camera:', error);\n      toast.error('Failed to access camera. Please check permissions.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const stopCamera = () => {\n    if (stream) {\n      stream.getTracks().forEach(track => track.stop());\n      setStream(null);\n    }\n    setIsCameraActive(false);\n    toast.info('Camera stopped');\n  };\n\n  const captureAndProcessFrame = async () => {\n    if (!videoRef.current || !canvasRef.current || isProcessing) return;\n\n    setIsProcessing(true);\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    if (!ctx) return;\n\n    // Set canvas dimensions to match video\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // Draw current video frame to canvas\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    try {\n      // Simulate face detection and recognition\n      await simulateFaceRecognition();\n    } catch (error) {\n      console.error('Face recognition error:', error);\n      toast.error('Face recognition failed');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const simulateFaceRecognition = async (): Promise<void> => {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        // Simulate detecting random students for demo\n        const numDetected = Math.floor(Math.random() * 3) + 1;\n        const detected: DetectedStudent[] = [];\n\n        for (let i = 0; i < numDetected; i++) {\n          const randomStudent = mockStudentDatabase[Math.floor(Math.random() * mockStudentDatabase.length)];\n          const confidence = 0.7 + Math.random() * 0.3; // 70-100% confidence\n          \n          if (!detected.find(s => s.id === randomStudent.id)) {\n            detected.push({\n              id: randomStudent.id,\n              name: randomStudent.name,\n              confidence,\n              timestamp: new Date(),\n              status: 'present'\n            });\n          }\n        }\n\n        setDetectedStudents(prev => {\n          const updated = [...prev];\n          detected.forEach(newStudent => {\n            const existingIndex = updated.findIndex(s => s.id === newStudent.id);\n            if (existingIndex >= 0) {\n              updated[existingIndex] = newStudent;\n            } else {\n              updated.push(newStudent);\n            }\n          });\n          return updated;\n        });\n\n        if (detected.length > 0) {\n          toast.success(`Detected ${detected.length} student(s)`);\n        }\n\n        resolve();\n      }, 2000);\n    });\n  };\n\n  const markAttendance = () => {\n    if (detectedStudents.length === 0) {\n      toast.error('No students detected. Please capture faces first.');\n      return;\n    }\n\n    // Mark attendance for all detected students\n    const attendanceData = detectedStudents.map(student => ({\n      ...student,\n      status: 'present' as const\n    }));\n\n    onAttendanceMarked?.(attendanceData);\n    toast.success(`Attendance marked for ${attendanceData.length} students`);\n    \n    // Clear detected students after marking attendance\n    setDetectedStudents([]);\n  };\n\n  const removeDetectedStudent = (studentId: string) => {\n    setDetectedStudents(prev => prev.filter(s => s.id !== studentId));\n  };\n\n  useEffect(() => {\n    return () => {\n      // Cleanup camera stream on unmount\n      if (stream) {\n        stream.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, [stream]);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Camera Control */}\n      <Card className=\"card-modern border-0\">\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Camera className=\"h-5 w-5 text-blue-600\" />\n                <span>AI Face Recognition Attendance</span>\n              </CardTitle>\n              <CardDescription>\n                Use AI-powered face recognition to automatically mark student attendance\n              </CardDescription>\n            </div>\n            <Badge variant={isCameraActive ? \"default\" : \"secondary\"}>\n              {isCameraActive ? \"Camera Active\" : \"Camera Inactive\"}\n            </Badge>\n          </div>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Video Feed */}\n          <div className=\"relative bg-gray-100 rounded-xl overflow-hidden\">\n            <video\n              ref={videoRef}\n              autoPlay\n              playsInline\n              muted\n              className=\"w-full h-64 object-cover\"\n              style={{ display: isCameraActive ? 'block' : 'none' }}\n            />\n            <canvas\n              ref={canvasRef}\n              className=\"hidden\"\n            />\n            {!isCameraActive && (\n              <div className=\"w-full h-64 flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200\">\n                <div className=\"text-center\">\n                  <CameraOff className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-500\">Camera not active</p>\n                </div>\n              </div>\n            )}\n            {isProcessing && (\n              <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\n                <div className=\"bg-white rounded-lg p-4 flex items-center space-x-2\">\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  <span className=\"text-sm\">Processing faces...</span>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Controls */}\n          <div className=\"flex space-x-3\">\n            {!isCameraActive ? (\n              <Button \n                onClick={startCamera} \n                disabled={isLoading}\n                className=\"btn-gradient\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                    Starting...\n                  </>\n                ) : (\n                  <>\n                    <Camera className=\"h-4 w-4 mr-2\" />\n                    Start Camera\n                  </>\n                )}\n              </Button>\n            ) : (\n              <>\n                <Button onClick={stopCamera} variant=\"outline\">\n                  <CameraOff className=\"h-4 w-4 mr-2\" />\n                  Stop Camera\n                </Button>\n                <Button \n                  onClick={captureAndProcessFrame}\n                  disabled={isProcessing}\n                  className=\"btn-gradient\"\n                >\n                  {isProcessing ? (\n                    <>\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Processing...\n                    </>\n                  ) : (\n                    <>\n                      <Users className=\"h-4 w-4 mr-2\" />\n                      Detect Faces\n                    </>\n                  )}\n                </Button>\n              </>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Detected Students */}\n      {detectedStudents.length > 0 && (\n        <Card className=\"card-modern border-0\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              <span>Detected Students ({detectedStudents.length})</span>\n            </CardTitle>\n            <CardDescription>\n              Review detected students before marking attendance\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {detectedStudents.map((student) => (\n                <div\n                  key={student.id}\n                  className=\"flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/20\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold\">\n                      {student.name.split(' ').map(n => n[0]).join('')}\n                    </div>\n                    <div>\n                      <p className=\"font-medium\">{student.name}</p>\n                      <p className=\"text-sm text-muted-foreground\">\n                        ID: {student.id} • Confidence: {(student.confidence * 100).toFixed(1)}%\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Badge variant=\"default\" className=\"bg-green-100 text-green-700\">\n                      Present\n                    </Badge>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => removeDetectedStudent(student.id)}\n                      className=\"h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600\"\n                    >\n                      <XCircle className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              ))}\n            </div>\n            \n            <div className=\"mt-6 flex justify-end\">\n              <Button onClick={markAttendance} className=\"btn-gradient\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Mark Attendance for {detectedStudents.length} Students\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AAsBO,SAAS,0BAA0B,EACxC,OAAO,EACP,kBAAkB,EACa;;IAC/B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEzD,iCAAiC;IACjC,MAAM,sBAAsB;QAC1B;YAAE,IAAI;YAAU,MAAM;YAAY,gBAAgB;QAAoB;QACtE;YAAE,IAAI;YAAU,MAAM;YAAc,gBAAgB;QAAoB;QACxE;YAAE,IAAI;YAAU,MAAM;YAAgB,gBAAgB;QAAoB;QAC1E;YAAE,IAAI;YAAU,MAAM;YAAgB,gBAAgB;QAAoB;QAC1E;YAAE,IAAI;YAAU,MAAM;YAAe,gBAAgB;QAAoB;KAC1E;IAED,MAAM,cAAc;QAClB,IAAI;YACF,aAAa;YACb,MAAM,cAAc,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAC5D,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,YAAY;gBACd;YACF;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,UAAU;gBACV,kBAAkB;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,UAAU;QACZ;QACA,kBAAkB;QAClB,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,IAAI,cAAc;QAE7D,gBAAgB;QAChB,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;QAEV,uCAAuC;QACvC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,qCAAqC;QACrC,IAAI,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAEtD,IAAI;YACF,0CAA0C;YAC1C,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B;QAC9B,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,8CAA8C;gBAC9C,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;gBACpD,MAAM,WAA8B,EAAE;gBAEtC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;oBACpC,MAAM,gBAAgB,mBAAmB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,oBAAoB,MAAM,EAAE;oBACjG,MAAM,aAAa,MAAM,KAAK,MAAM,KAAK,KAAK,qBAAqB;oBAEnE,IAAI,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,EAAE,GAAG;wBAClD,SAAS,IAAI,CAAC;4BACZ,IAAI,cAAc,EAAE;4BACpB,MAAM,cAAc,IAAI;4BACxB;4BACA,WAAW,IAAI;4BACf,QAAQ;wBACV;oBACF;gBACF;gBAEA,oBAAoB,CAAA;oBAClB,MAAM,UAAU;2BAAI;qBAAK;oBACzB,SAAS,OAAO,CAAC,CAAA;wBACf,MAAM,gBAAgB,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;wBACnE,IAAI,iBAAiB,GAAG;4BACtB,OAAO,CAAC,cAAc,GAAG;wBAC3B,OAAO;4BACL,QAAQ,IAAI,CAAC;wBACf;oBACF;oBACA,OAAO;gBACT;gBAEA,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,WAAW,CAAC;gBACxD;gBAEA;YACF,GAAG;QACL;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,4CAA4C;QAC5C,MAAM,iBAAiB,iBAAiB,GAAG,CAAC,CAAA,UAAW,CAAC;gBACtD,GAAG,OAAO;gBACV,QAAQ;YACV,CAAC;QAED,qBAAqB;QACrB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,eAAe,MAAM,CAAC,SAAS,CAAC;QAEvE,mDAAmD;QACnD,oBAAoB,EAAE;IACxB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACxD;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR;uDAAO;oBACL,mCAAmC;oBACnC,IAAI,QAAQ;wBACV,OAAO,SAAS,GAAG,OAAO;mEAAC,CAAA,QAAS,MAAM,IAAI;;oBAChD;gBACF;;QACF;8CAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAS,iBAAiB,YAAY;8CAC1C,iBAAiB,kBAAkB;;;;;;;;;;;;;;;;;kCAI1C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK;wCACL,QAAQ;wCACR,WAAW;wCACX,KAAK;wCACL,WAAU;wCACV,OAAO;4CAAE,SAAS,iBAAiB,UAAU;wCAAO;;;;;;kDAEtD,6LAAC;wCACC,KAAK;wCACL,WAAU;;;;;;oCAEX,CAAC,gCACA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;oCAIlC,8BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAOlC,6LAAC;gCAAI,WAAU;0CACZ,CAAC,+BACA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;yDAMzC;;sDACE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAY,SAAQ;;8DACnC,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYjD,iBAAiB,MAAM,GAAG,mBACzB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;;4CAAK;4CAAoB,iBAAiB,MAAM;4CAAC;;;;;;;;;;;;;0CAEpD,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kEAE/C,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAe,QAAQ,IAAI;;;;;;0EACxC,6LAAC;gEAAE,WAAU;;oEAAgC;oEACtC,QAAQ,EAAE;oEAAC;oEAAgB,CAAC,QAAQ,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAI5E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAA8B;;;;;;kEAGjE,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,sBAAsB,QAAQ,EAAE;wDAC/C,WAAU;kEAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;;;;;;;uCAxBlB,QAAQ,EAAE;;;;;;;;;;0CA+BrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAgB,WAAU;;sDACzC,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;wCACnB,iBAAiB,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D;GA/TgB;KAAA", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ai/AIChatbot.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { MessageSquare, Send, Bot, User, Loader2, X, Minimize2, Maximize2 } from 'lucide-react';\nimport { toast } from 'sonner';\n\ninterface ChatMessage {\n  id: string;\n  type: 'user' | 'bot';\n  content: string;\n  timestamp: Date;\n  isTyping?: boolean;\n}\n\ninterface AIChatbotProps {\n  userType?: 'student' | 'parent' | 'teacher' | 'admin';\n  isMinimized?: boolean;\n  onToggleMinimize?: () => void;\n  onClose?: () => void;\n}\n\nexport function AIChatbot({ \n  userType = 'student', \n  isMinimized = false,\n  onToggleMinimize,\n  onClose \n}: AIChatbotProps) {\n  const [messages, setMessages] = useState<ChatMessage[]>([\n    {\n      id: '1',\n      type: 'bot',\n      content: `Hello! I'm your AI assistant. I can help you with questions about school policies, schedules, fees, and more. How can I assist you today?`,\n      timestamp: new Date()\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // FAQ Knowledge Base\n  const faqDatabase = {\n    student: [\n      {\n        keywords: ['schedule', 'timetable', 'class', 'time'],\n        response: 'You can view your class schedule in the Timetable section of your dashboard. Classes typically run from 8:00 AM to 3:00 PM, Monday through Friday.'\n      },\n      {\n        keywords: ['fees', 'payment', 'tuition', 'cost'],\n        response: 'School fees can be paid online through the Fees section in your dashboard. You can also view your payment history and download receipts there.'\n      },\n      {\n        keywords: ['attendance', 'absent', 'leave'],\n        response: 'You can view your attendance record in the Attendance section. If you need to apply for leave, please use the Leave Management feature or contact your class teacher.'\n      },\n      {\n        keywords: ['grades', 'marks', 'results', 'exam'],\n        response: 'Your exam results and grades are available in the Performance section of your dashboard. Results are typically published within 2 weeks of exam completion.'\n      },\n      {\n        keywords: ['library', 'books', 'borrow'],\n        response: 'The library is open from 8:00 AM to 5:00 PM. You can search for books and check your borrowed books in the Library section of your dashboard.'\n      }\n    ],\n    parent: [\n      {\n        keywords: ['fees', 'payment', 'tuition', 'bill'],\n        response: 'You can view and pay your child\\'s school fees through the parent portal. Payment can be made online using credit/debit cards or bank transfer.'\n      },\n      {\n        keywords: ['attendance', 'absent', 'sick'],\n        response: 'You can view your child\\'s attendance record in the parent dashboard. To report absence due to illness, please contact the school office or use the leave application feature.'\n      },\n      {\n        keywords: ['teacher', 'contact', 'meeting', 'appointment'],\n        response: 'You can contact your child\\'s teachers through the messaging system in the parent portal. Parent-teacher meetings are scheduled quarterly.'\n      },\n      {\n        keywords: ['transport', 'bus', 'pickup', 'drop'],\n        response: 'School transport information including routes, timings, and fees are available in the Transport section. Contact the transport coordinator for any changes.'\n      }\n    ],\n    teacher: [\n      {\n        keywords: ['attendance', 'mark', 'student'],\n        response: 'You can mark student attendance through the Attendance section in your teacher dashboard. Attendance should be marked within the first 15 minutes of each class.'\n      },\n      {\n        keywords: ['grades', 'marks', 'assessment'],\n        response: 'Student grades can be entered through the Grades section. Make sure to submit grades before the deadline specified by the administration.'\n      },\n      {\n        keywords: ['leave', 'substitute', 'absent'],\n        response: 'To apply for leave, use the Leave Management system. Please ensure you arrange for a substitute teacher and inform the administration in advance.'\n      }\n    ],\n    admin: [\n      {\n        keywords: ['reports', 'analytics', 'data'],\n        response: 'Comprehensive reports and analytics are available in the Analytics dashboard. You can generate custom reports for attendance, fees, performance, and more.'\n      },\n      {\n        keywords: ['backup', 'data', 'export'],\n        response: 'Data backup and export features are available in the Settings section. Regular automated backups are performed daily at 2:00 AM.'\n      }\n    ]\n  };\n\n  const quickReplies = {\n    student: ['View Schedule', 'Check Fees', 'Attendance Record', 'Library Books'],\n    parent: ['Pay Fees', 'Child\\'s Attendance', 'Contact Teacher', 'Transport Info'],\n    teacher: ['Mark Attendance', 'Enter Grades', 'Apply Leave', 'Class Schedule'],\n    admin: ['Generate Reports', 'System Settings', 'User Management', 'Data Export']\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const generateBotResponse = async (userMessage: string): Promise<string> => {\n    const userFAQs = faqDatabase[userType] || faqDatabase.student;\n    const lowerMessage = userMessage.toLowerCase();\n\n    // Find matching FAQ\n    const matchingFAQ = userFAQs.find(faq => \n      faq.keywords.some(keyword => lowerMessage.includes(keyword))\n    );\n\n    if (matchingFAQ) {\n      return matchingFAQ.response;\n    }\n\n    // Default responses for common greetings\n    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {\n      return `Hello! I'm here to help you with any questions about school. You can ask me about schedules, fees, attendance, or any other school-related topics.`;\n    }\n\n    if (lowerMessage.includes('thank')) {\n      return `You're welcome! Is there anything else I can help you with?`;\n    }\n\n    // Default fallback response\n    return `I understand you're asking about \"${userMessage}\". While I don't have specific information about that topic, you can:\n\n• Contact the school office at (555) 123-4567\n• Email <NAME_EMAIL>\n• Visit the main office during school hours\n• Check the relevant section in your dashboard\n\nIs there anything else I can help you with?`;\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage.trim(),\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    // Add typing indicator\n    const typingMessage: ChatMessage = {\n      id: 'typing',\n      type: 'bot',\n      content: '',\n      timestamp: new Date(),\n      isTyping: true\n    };\n    setMessages(prev => [...prev, typingMessage]);\n\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));\n      \n      const botResponse = await generateBotResponse(userMessage.content);\n      \n      // Remove typing indicator and add actual response\n      setMessages(prev => {\n        const filtered = prev.filter(msg => msg.id !== 'typing');\n        return [...filtered, {\n          id: (Date.now() + 1).toString(),\n          type: 'bot',\n          content: botResponse,\n          timestamp: new Date()\n        }];\n      });\n    } catch (error) {\n      console.error('Error generating response:', error);\n      setMessages(prev => {\n        const filtered = prev.filter(msg => msg.id !== 'typing');\n        return [...filtered, {\n          id: (Date.now() + 1).toString(),\n          type: 'bot',\n          content: 'I apologize, but I\\'m having trouble processing your request right now. Please try again or contact the school office directly.',\n          timestamp: new Date()\n        }];\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleQuickReply = (reply: string) => {\n    setInputMessage(reply);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  if (isMinimized) {\n    return (\n      <div className=\"fixed bottom-4 right-4 z-50\">\n        <Button\n          onClick={onToggleMinimize}\n          className=\"btn-gradient rounded-full w-14 h-14 shadow-lg hover:shadow-xl\"\n        >\n          <MessageSquare className=\"h-6 w-6\" />\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)]\">\n      <Card className=\"card-modern border-0 shadow-2xl\">\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\">\n                <Bot className=\"h-4 w-4 text-white\" />\n              </div>\n              <div>\n                <CardTitle className=\"text-sm\">AI Assistant</CardTitle>\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  Online\n                </Badge>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onToggleMinimize}\n                className=\"h-8 w-8 p-0\"\n              >\n                <Minimize2 className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onClose}\n                className=\"h-8 w-8 p-0\"\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent className=\"p-0\">\n          {/* Messages */}\n          <div className=\"h-80 overflow-y-auto p-4 space-y-3\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-[80%] rounded-xl p-3 ${\n                    message.type === 'user'\n                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'\n                      : 'bg-gray-100 text-gray-900'\n                  }`}\n                >\n                  {message.isTyping ? (\n                    <div className=\"flex items-center space-x-1\">\n                      <div className=\"flex space-x-1\">\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                      </div>\n                      <span className=\"text-xs text-gray-500 ml-2\">AI is typing...</span>\n                    </div>\n                  ) : (\n                    <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                  )}\n                </div>\n              </div>\n            ))}\n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Quick Replies */}\n          <div className=\"px-4 pb-3\">\n            <div className=\"flex flex-wrap gap-2\">\n              {quickReplies[userType]?.map((reply) => (\n                <Button\n                  key={reply}\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleQuickReply(reply)}\n                  className=\"text-xs\"\n                >\n                  {reply}\n                </Button>\n              ))}\n            </div>\n          </div>\n\n          {/* Input */}\n          <div className=\"p-4 border-t border-border/50\">\n            <div className=\"flex space-x-2\">\n              <Input\n                value={inputMessage}\n                onChange={(e) => setInputMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Type your message...\"\n                className=\"input-modern\"\n                disabled={isLoading}\n              />\n              <Button\n                onClick={handleSendMessage}\n                disabled={!inputMessage.trim() || isLoading}\n                className=\"btn-gradient\"\n                size=\"sm\"\n              >\n                {isLoading ? (\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                ) : (\n                  <Send className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAyBO,SAAS,UAAU,EACxB,WAAW,SAAS,EACpB,cAAc,KAAK,EACnB,gBAAgB,EAChB,OAAO,EACQ;;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD;YACE,IAAI;YACJ,MAAM;YACN,SAAS,CAAC,yIAAyI,CAAC;YACpJ,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,qBAAqB;IACrB,MAAM,cAAc;QAClB,SAAS;YACP;gBACE,UAAU;oBAAC;oBAAY;oBAAa;oBAAS;iBAAO;gBACpD,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAQ;oBAAW;oBAAW;iBAAO;gBAChD,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAc;oBAAU;iBAAQ;gBAC3C,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAU;oBAAS;oBAAW;iBAAO;gBAChD,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAW;oBAAS;iBAAS;gBACxC,UAAU;YACZ;SACD;QACD,QAAQ;YACN;gBACE,UAAU;oBAAC;oBAAQ;oBAAW;oBAAW;iBAAO;gBAChD,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAc;oBAAU;iBAAO;gBAC1C,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAW;oBAAW;oBAAW;iBAAc;gBAC1D,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAa;oBAAO;oBAAU;iBAAO;gBAChD,UAAU;YACZ;SACD;QACD,SAAS;YACP;gBACE,UAAU;oBAAC;oBAAc;oBAAQ;iBAAU;gBAC3C,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAU;oBAAS;iBAAa;gBAC3C,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAS;oBAAc;iBAAS;gBAC3C,UAAU;YACZ;SACD;QACD,OAAO;YACL;gBACE,UAAU;oBAAC;oBAAW;oBAAa;iBAAO;gBAC1C,UAAU;YACZ;YACA;gBACE,UAAU;oBAAC;oBAAU;oBAAQ;iBAAS;gBACtC,UAAU;YACZ;SACD;IACH;IAEA,MAAM,eAAe;QACnB,SAAS;YAAC;YAAiB;YAAc;YAAqB;SAAgB;QAC9E,QAAQ;YAAC;YAAY;YAAuB;YAAmB;SAAiB;QAChF,SAAS;YAAC;YAAmB;YAAgB;YAAe;SAAiB;QAC7E,OAAO;YAAC;YAAoB;YAAmB;YAAmB;SAAc;IAClF;IAEA,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAS;IAEb,MAAM,sBAAsB,OAAO;QACjC,MAAM,WAAW,WAAW,CAAC,SAAS,IAAI,YAAY,OAAO;QAC7D,MAAM,eAAe,YAAY,WAAW;QAE5C,oBAAoB;QACpB,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,MAChC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC;QAGrD,IAAI,aAAa;YACf,OAAO,YAAY,QAAQ;QAC7B;QAEA,yCAAyC;QACzC,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,OAAO;YACjE,OAAO,CAAC,kJAAkJ,CAAC;QAC7J;QAEA,IAAI,aAAa,QAAQ,CAAC,UAAU;YAClC,OAAO,CAAC,2DAA2D,CAAC;QACtE;QAEA,4BAA4B;QAC5B,OAAO,CAAC,kCAAkC,EAAE,YAAY;;;;;;;2CAOjB,CAAC;IAC1C;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAA2B;YAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS,aAAa,IAAI;YAC1B,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,uBAAuB;QACvB,MAAM,gBAA6B;YACjC,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;YACf,UAAU;QACZ;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAE5C,IAAI;YACF,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,KAAK,MAAM,KAAK;YAExE,MAAM,cAAc,MAAM,oBAAoB,YAAY,OAAO;YAEjE,kDAAkD;YAClD,YAAY,CAAA;gBACV,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBAC/C,OAAO;uBAAI;oBAAU;wBACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;wBAC7B,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI;oBACjB;iBAAE;YACJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,YAAY,CAAA;gBACV,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBAC/C,OAAO;uBAAI;oBAAU;wBACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;wBAC7B,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI;oBACjB;iBAAE;YACJ;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,6LAAC;;0DACC,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAU;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKrB,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;kDAE9E,cAAA,6LAAC;4CACC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SACb,0DACA,6BACJ;sDAED,QAAQ,QAAQ,iBACf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAAkD,OAAO;oEAAE,gBAAgB;gEAAO;;;;;;0EACjG,6LAAC;gEAAI,WAAU;gEAAkD,OAAO;oEAAE,gBAAgB;gEAAO;;;;;;;;;;;;kEAEnG,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;qEAG/C,6LAAC;gDAAE,WAAU;0DAA+B,QAAQ,OAAO;;;;;;;;;;;uCApB1D,QAAQ,EAAE;;;;;8CAyBnB,6LAAC;oCAAI,KAAK;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,sBAC5B,6LAAC,qIAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDAET;uCANI;;;;;;;;;;;;;;;sCAab,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,YAAY;wCACZ,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,aAAa,IAAI,MAAM;wCAClC,WAAU;wCACV,MAAK;kDAEJ,0BACC,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;GAzUgB;KAAA", "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ai/PredictiveAnalytics.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { \n  TrendingDown, \n  TrendingUp, \n  AlertTriangle, \n  Users, \n  DollarSign, \n  GraduationCap,\n  Brain,\n  Target,\n  Zap\n} from 'lucide-react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';\n\ninterface PredictionResult {\n  studentId: string;\n  studentName: string;\n  riskLevel: 'low' | 'medium' | 'high';\n  riskScore: number;\n  factors: string[];\n  recommendation: string;\n}\n\ninterface AnalyticsData {\n  dropoutPredictions: PredictionResult[];\n  feeDefaultPredictions: PredictionResult[];\n  performanceTrends: any[];\n  attendancePatterns: any[];\n}\n\nexport function PredictiveAnalytics() {\n  const [selectedModel, setSelectedModel] = useState<'dropout' | 'fee_default' | 'performance'>('dropout');\n  const [isLoading, setIsLoading] = useState(false);\n  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);\n\n  // Mock predictive analytics data\n  const mockAnalyticsData: AnalyticsData = {\n    dropoutPredictions: [\n      {\n        studentId: 'STU001',\n        studentName: 'John Doe',\n        riskLevel: 'high',\n        riskScore: 0.85,\n        factors: ['Low attendance (65%)', 'Declining grades', 'Financial difficulties'],\n        recommendation: 'Immediate intervention required. Schedule counseling session and financial aid review.'\n      },\n      {\n        studentId: 'STU002',\n        studentName: 'Jane Smith',\n        riskLevel: 'medium',\n        riskScore: 0.62,\n        factors: ['Irregular attendance', 'Family issues'],\n        recommendation: 'Monitor closely. Provide additional academic support and family counseling.'\n      },\n      {\n        studentId: 'STU003',\n        studentName: 'Mike Johnson',\n        riskLevel: 'low',\n        riskScore: 0.23,\n        factors: ['Good academic performance', 'Regular attendance'],\n        recommendation: 'Continue current support. Student is performing well.'\n      }\n    ],\n    feeDefaultPredictions: [\n      {\n        studentId: 'STU004',\n        studentName: 'Sarah Wilson',\n        riskLevel: 'high',\n        riskScore: 0.78,\n        factors: ['Previous late payments', 'Economic background', 'Parent job loss'],\n        recommendation: 'Offer payment plan options and financial assistance programs.'\n      },\n      {\n        studentId: 'STU005',\n        studentName: 'David Brown',\n        riskLevel: 'medium',\n        riskScore: 0.55,\n        factors: ['Seasonal payment delays', 'Multiple siblings'],\n        recommendation: 'Provide flexible payment schedule and sibling discount information.'\n      }\n    ],\n    performanceTrends: [\n      { month: 'Jan', predicted: 85, actual: 82 },\n      { month: 'Feb', predicted: 87, actual: 85 },\n      { month: 'Mar', predicted: 84, actual: 86 },\n      { month: 'Apr', predicted: 88, actual: 87 },\n      { month: 'May', predicted: 86, actual: null },\n      { month: 'Jun', predicted: 89, actual: null }\n    ],\n    attendancePatterns: [\n      { day: 'Monday', attendance: 92 },\n      { day: 'Tuesday', attendance: 95 },\n      { day: 'Wednesday', attendance: 94 },\n      { day: 'Thursday', attendance: 93 },\n      { day: 'Friday', attendance: 88 }\n    ]\n  };\n\n  const runPredictiveAnalysis = async () => {\n    setIsLoading(true);\n    \n    // Simulate AI model processing\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setAnalyticsData(mockAnalyticsData);\n    setIsLoading(false);\n  };\n\n  useEffect(() => {\n    runPredictiveAnalysis();\n  }, []);\n\n  const getRiskColor = (riskLevel: string) => {\n    switch (riskLevel) {\n      case 'high': return 'text-red-600 bg-red-50 border-red-200';\n      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n      case 'low': return 'text-green-600 bg-green-50 border-green-200';\n      default: return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n  };\n\n  const getRiskIcon = (riskLevel: string) => {\n    switch (riskLevel) {\n      case 'high': return <AlertTriangle className=\"h-4 w-4\" />;\n      case 'medium': return <TrendingDown className=\"h-4 w-4\" />;\n      case 'low': return <TrendingUp className=\"h-4 w-4\" />;\n      default: return <Target className=\"h-4 w-4\" />;\n    }\n  };\n\n  const currentPredictions = selectedModel === 'dropout' \n    ? analyticsData?.dropoutPredictions \n    : analyticsData?.feeDefaultPredictions;\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"space-y-2\">\n          <h1 className=\"text-4xl font-bold gradient-text flex items-center space-x-3\">\n            <Brain className=\"h-8 w-8 text-blue-600\" />\n            <span>AI Predictive Analytics</span>\n          </h1>\n          <p className=\"text-muted-foreground text-lg\">\n            Advanced AI models to predict student outcomes and optimize interventions\n          </p>\n        </div>\n        <Button \n          onClick={runPredictiveAnalysis} \n          disabled={isLoading}\n          className=\"btn-gradient\"\n        >\n          {isLoading ? (\n            <>\n              <Zap className=\"h-4 w-4 mr-2 animate-pulse\" />\n              Analyzing...\n            </>\n          ) : (\n            <>\n              <Zap className=\"h-4 w-4 mr-2\" />\n              Run Analysis\n            </>\n          )}\n        </Button>\n      </div>\n\n      {/* Model Selection */}\n      <Card className=\"card-modern border-0\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Target className=\"h-5 w-5 text-purple-600\" />\n            <span>Prediction Models</span>\n          </CardTitle>\n          <CardDescription>\n            Select the AI model to view predictions and insights\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Select value={selectedModel} onValueChange={(value: any) => setSelectedModel(value)}>\n            <SelectTrigger className=\"w-64\">\n              <SelectValue placeholder=\"Select prediction model\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"dropout\">Student Dropout Risk</SelectItem>\n              <SelectItem value=\"fee_default\">Fee Payment Default</SelectItem>\n              <SelectItem value=\"performance\">Academic Performance</SelectItem>\n            </SelectContent>\n          </Select>\n        </CardContent>\n      </Card>\n\n      {/* Predictions Results */}\n      {currentPredictions && (\n        <Card className=\"card-modern border-0\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              {selectedModel === 'dropout' ? (\n                <>\n                  <Users className=\"h-5 w-5 text-red-600\" />\n                  <span>Student Dropout Risk Predictions</span>\n                </>\n              ) : (\n                <>\n                  <DollarSign className=\"h-5 w-5 text-orange-600\" />\n                  <span>Fee Payment Default Predictions</span>\n                </>\n              )}\n            </CardTitle>\n            <CardDescription>\n              AI-powered predictions with risk scores and recommended interventions\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {currentPredictions.map((prediction) => (\n                <div\n                  key={prediction.studentId}\n                  className=\"p-6 bg-white/50 rounded-xl border border-white/20 hover:bg-white/70 transition-all duration-200\"\n                >\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold\">\n                        {prediction.studentName.split(' ').map(n => n[0]).join('')}\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-lg\">{prediction.studentName}</h3>\n                        <p className=\"text-sm text-muted-foreground\">ID: {prediction.studentId}</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <Badge className={`${getRiskColor(prediction.riskLevel)} border`}>\n                        {getRiskIcon(prediction.riskLevel)}\n                        <span className=\"ml-1 capitalize\">{prediction.riskLevel} Risk</span>\n                      </Badge>\n                      <p className=\"text-sm text-muted-foreground mt-1\">\n                        Score: {(prediction.riskScore * 100).toFixed(1)}%\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <h4 className=\"font-medium text-sm text-muted-foreground mb-2\">Risk Factors</h4>\n                      <ul className=\"space-y-1\">\n                        {prediction.factors.map((factor, index) => (\n                          <li key={index} className=\"text-sm flex items-center space-x-2\">\n                            <div className=\"w-1.5 h-1.5 bg-red-400 rounded-full\"></div>\n                            <span>{factor}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-sm text-muted-foreground mb-2\">Recommendation</h4>\n                      <p className=\"text-sm bg-blue-50 p-3 rounded-lg border border-blue-200\">\n                        {prediction.recommendation}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Analytics Charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Performance Trends */}\n        <Card className=\"card-modern border-0 bg-gradient-to-br from-white to-purple-50/30\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <GraduationCap className=\"h-5 w-5 text-purple-600\" />\n              <span>Performance Prediction vs Actual</span>\n            </CardTitle>\n            <CardDescription>\n              AI model predictions compared to actual performance\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={analyticsData?.performanceTrends}>\n                <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#e2e8f0\" />\n                <XAxis dataKey=\"month\" stroke=\"#64748b\" fontSize={12} />\n                <YAxis stroke=\"#64748b\" fontSize={12} />\n                <Tooltip \n                  contentStyle={{ \n                    backgroundColor: 'white', \n                    border: 'none', \n                    borderRadius: '12px', \n                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' \n                  }} \n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"predicted\" \n                  stroke=\"#8b5cf6\" \n                  strokeWidth={3}\n                  strokeDasharray=\"5 5\"\n                  name=\"AI Prediction\"\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"actual\" \n                  stroke=\"#3b82f6\" \n                  strokeWidth={3}\n                  name=\"Actual Performance\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n\n        {/* Attendance Patterns */}\n        <Card className=\"card-modern border-0 bg-gradient-to-br from-white to-green-50/30\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Users className=\"h-5 w-5 text-green-600\" />\n              <span>Weekly Attendance Patterns</span>\n            </CardTitle>\n            <CardDescription>\n              AI-identified patterns in student attendance\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={analyticsData?.attendancePatterns}>\n                <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#e2e8f0\" />\n                <XAxis dataKey=\"day\" stroke=\"#64748b\" fontSize={12} />\n                <YAxis stroke=\"#64748b\" fontSize={12} />\n                <Tooltip \n                  contentStyle={{ \n                    backgroundColor: 'white', \n                    border: 'none', \n                    borderRadius: '12px', \n                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' \n                  }} \n                />\n                <Bar \n                  dataKey=\"attendance\" \n                  fill=\"#10b981\" \n                  radius={[4, 4, 0, 0]}\n                  name=\"Attendance %\"\n                />\n              </BarChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* AI Insights */}\n      <Card className=\"card-modern border-0 bg-gradient-to-br from-white to-blue-50/30\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Brain className=\"h-5 w-5 text-blue-600\" />\n            <span>AI-Generated Insights</span>\n          </CardTitle>\n          <CardDescription>\n            Key insights and recommendations from AI analysis\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"p-4 bg-white/50 rounded-xl border border-white/20\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <AlertTriangle className=\"h-5 w-5 text-red-500\" />\n                <h3 className=\"font-semibold text-red-700\">High Risk Students</h3>\n              </div>\n              <p className=\"text-2xl font-bold text-red-600\">3</p>\n              <p className=\"text-sm text-muted-foreground\">Require immediate intervention</p>\n            </div>\n            \n            <div className=\"p-4 bg-white/50 rounded-xl border border-white/20\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <TrendingUp className=\"h-5 w-5 text-green-500\" />\n                <h3 className=\"font-semibold text-green-700\">Improvement Rate</h3>\n              </div>\n              <p className=\"text-2xl font-bold text-green-600\">87%</p>\n              <p className=\"text-sm text-muted-foreground\">Students showing improvement</p>\n            </div>\n            \n            <div className=\"p-4 bg-white/50 rounded-xl border border-white/20\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <Target className=\"h-5 w-5 text-blue-500\" />\n                <h3 className=\"font-semibold text-blue-700\">Model Accuracy</h3>\n              </div>\n              <p className=\"text-2xl font-bold text-blue-600\">94.2%</p>\n              <p className=\"text-sm text-muted-foreground\">Prediction accuracy rate</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAlBA;;;;;;;;AAoCO,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAC9F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAEzE,iCAAiC;IACjC,MAAM,oBAAmC;QACvC,oBAAoB;YAClB;gBACE,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,SAAS;oBAAC;oBAAwB;oBAAoB;iBAAyB;gBAC/E,gBAAgB;YAClB;YACA;gBACE,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,SAAS;oBAAC;oBAAwB;iBAAgB;gBAClD,gBAAgB;YAClB;YACA;gBACE,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,SAAS;oBAAC;oBAA6B;iBAAqB;gBAC5D,gBAAgB;YAClB;SACD;QACD,uBAAuB;YACrB;gBACE,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,SAAS;oBAAC;oBAA0B;oBAAuB;iBAAkB;gBAC7E,gBAAgB;YAClB;YACA;gBACE,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,SAAS;oBAAC;oBAA2B;iBAAoB;gBACzD,gBAAgB;YAClB;SACD;QACD,mBAAmB;YACjB;gBAAE,OAAO;gBAAO,WAAW;gBAAI,QAAQ;YAAG;YAC1C;gBAAE,OAAO;gBAAO,WAAW;gBAAI,QAAQ;YAAG;YAC1C;gBAAE,OAAO;gBAAO,WAAW;gBAAI,QAAQ;YAAG;YAC1C;gBAAE,OAAO;gBAAO,WAAW;gBAAI,QAAQ;YAAG;YAC1C;gBAAE,OAAO;gBAAO,WAAW;gBAAI,QAAQ;YAAK;YAC5C;gBAAE,OAAO;gBAAO,WAAW;gBAAI,QAAQ;YAAK;SAC7C;QACD,oBAAoB;YAClB;gBAAE,KAAK;gBAAU,YAAY;YAAG;YAChC;gBAAE,KAAK;gBAAW,YAAY;YAAG;YACjC;gBAAE,KAAK;gBAAa,YAAY;YAAG;YACnC;gBAAE,KAAK;gBAAY,YAAY;YAAG;YAClC;gBAAE,KAAK;gBAAU,YAAY;YAAG;SACjC;IACH;IAEA,MAAM,wBAAwB;QAC5B,aAAa;QAEb,+BAA+B;QAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,iBAAiB;QACjB,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR;QACF;wCAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAQ,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAC7C,KAAK;gBAAU,qBAAO,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YAC9C,KAAK;gBAAO,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YACzC;gBAAS,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,qBAAqB,kBAAkB,YACzC,eAAe,qBACf,eAAe;IAEnB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAI/C,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,0BACC;;8CACE,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAA+B;;yDAIhD;;8CACE,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;0BAQxC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAe,eAAe,CAAC,QAAe,iBAAiB;;8CAC5E,6LAAC,qIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;8CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sDACZ,6LAAC,qIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAU;;;;;;sDAC5B,6LAAC,qIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAc;;;;;;sDAChC,6LAAC,qIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOvC,oCACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAClB,kBAAkB,0BACjB;;sDACE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;iEAGR;;sDACE,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;sDAAK;;;;;;;;;;;;;0CAIZ,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,2BACvB,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,WAAW,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;sEAEzD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAyB,WAAW,WAAW;;;;;;8EAC7D,6LAAC;oEAAE,WAAU;;wEAAgC;wEAAK,WAAW,SAAS;;;;;;;;;;;;;;;;;;;8DAG1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAW,GAAG,aAAa,WAAW,SAAS,EAAE,OAAO,CAAC;;gEAC7D,YAAY,WAAW,SAAS;8EACjC,6LAAC;oEAAK,WAAU;;wEAAmB,WAAW,SAAS;wEAAC;;;;;;;;;;;;;sEAE1D,6LAAC;4DAAE,WAAU;;gEAAqC;gEACxC,CAAC,WAAW,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;sDAKtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAiD;;;;;;sEAC/D,6LAAC;4DAAG,WAAU;sEACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/B,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;;;;;;8DAOf,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAiD;;;;;;sEAC/D,6LAAC;4DAAE,WAAU;sEACV,WAAW,cAAc;;;;;;;;;;;;;;;;;;;mCAvC3B,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;0BAmDrC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wCAAC,MAAM,eAAe;;0DAC9B,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;gDAAM,QAAO;;;;;;0DAC5C,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAQ,QAAO;gDAAU,UAAU;;;;;;0DAClD,6LAAC,wJAAA,CAAA,QAAK;gDAAC,QAAO;gDAAU,UAAU;;;;;;0DAClC,6LAAC,0JAAA,CAAA,UAAO;gDACN,cAAc;oDACZ,iBAAiB;oDACjB,QAAQ;oDACR,cAAc;oDACd,WAAW;gDACb;;;;;;0DAEF,6LAAC,uJAAA,CAAA,OAAI;gDACH,MAAK;gDACL,SAAQ;gDACR,QAAO;gDACP,aAAa;gDACb,iBAAgB;gDAChB,MAAK;;;;;;0DAEP,6LAAC,uJAAA,CAAA,OAAI;gDACH,MAAK;gDACL,SAAQ;gDACR,QAAO;gDACP,aAAa;gDACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQf,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM,eAAe;;0DAC7B,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;gDAAM,QAAO;;;;;;0DAC5C,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAM,QAAO;gDAAU,UAAU;;;;;;0DAChD,6LAAC,wJAAA,CAAA,QAAK;gDAAC,QAAO;gDAAU,UAAU;;;;;;0DAClC,6LAAC,0JAAA,CAAA,UAAO;gDACN,cAAc;oDACZ,iBAAiB;oDACjB,QAAQ;oDACR,cAAc;oDACd,WAAW;gDACb;;;;;;0DAEF,6LAAC,sJAAA,CAAA,MAAG;gDACF,SAAQ;gDACR,MAAK;gDACL,QAAQ;oDAAC;oDAAG;oDAAG;oDAAG;iDAAE;gDACpB,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;;;;;;;sDAE7C,6LAAC;4CAAE,WAAU;sDAAkC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;;;;;;;sDAE/C,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;;;;;;;sDAE9C,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D;GA5WgB;KAAA", "debugId": null}}, {"offset": {"line": 2915, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/dashboard/ai-features/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Brain,\n  Camera,\n  MessageSquare,\n  TrendingUp,\n  Zap,\n  Users,\n  Bot,\n  Target,\n  Sparkles,\n  ChevronRight\n} from 'lucide-react';\nimport { FaceRecognitionAttendance } from '@/components/ai/FaceRecognitionAttendance';\nimport { AIChatbot } from '@/components/ai/AIChatbot';\nimport { PredictiveAnalytics } from '@/components/ai/PredictiveAnalytics';\nimport { toast } from 'sonner';\n\nexport default function AIFeaturesPage() {\n  const [activeFeature, setActiveFeature] = useState<string>('overview');\n  const [isChatbotOpen, setIsChatbotOpen] = useState(false);\n  const [isChatbotMinimized, setIsChatbotMinimized] = useState(false);\n\n  const aiFeatures = [\n    {\n      id: 'face-recognition',\n      title: 'Face Recognition Attendance',\n      description: 'AI-powered automatic attendance marking using facial recognition technology',\n      icon: Camera,\n      status: 'active',\n      accuracy: '96.8%',\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      id: 'chatbot',\n      title: 'AI Assistant Chatbot',\n      description: 'Intelligent chatbot for answering student, parent, and teacher queries',\n      icon: MessageSquare,\n      status: 'active',\n      accuracy: '94.2%',\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      id: 'predictive-analytics',\n      title: 'Predictive Analytics',\n      description: 'Advanced ML models for predicting student outcomes and risks',\n      icon: TrendingUp,\n      status: 'active',\n      accuracy: '91.5%',\n      color: 'from-purple-500 to-purple-600'\n    }\n  ];\n\n  const handleAttendanceMarked = (students: any[]) => {\n    toast.success(`Attendance marked for ${students.length} students using AI`);\n  };\n\n  const toggleChatbot = () => {\n    setIsChatbotOpen(!isChatbotOpen);\n    setIsChatbotMinimized(false);\n  };\n\n  const toggleChatbotMinimize = () => {\n    setIsChatbotMinimized(!isChatbotMinimized);\n  };\n\n  const closeChatbot = () => {\n    setIsChatbotOpen(false);\n    setIsChatbotMinimized(false);\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"relative\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-3xl\"></div>\n        <div className=\"relative bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-xl\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\">\n              <Brain className=\"h-8 w-8 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-4xl font-bold gradient-text\">AI-Powered Features</h1>\n              <p className=\"text-muted-foreground text-lg\">Advanced artificial intelligence for modern education management</p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-6 text-sm text-muted-foreground\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>All AI Models Online</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Zap className=\"h-4 w-4 text-yellow-500\" />\n              <span>Real-time Processing</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Target className=\"h-4 w-4 text-blue-500\" />\n              <span>94.2% Average Accuracy</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* AI Features Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        {aiFeatures.map((feature) => (\n          <Card key={feature.id} className=\"card-modern border-0 hover:shadow-2xl transition-all duration-300 group cursor-pointer\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className={`w-12 h-12 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200`}>\n                  <feature.icon className=\"h-6 w-6 text-white\" />\n                </div>\n                <Badge variant=\"default\" className=\"bg-green-100 text-green-700\">\n                  {feature.status}\n                </Badge>\n              </div>\n\n              <h3 className=\"text-lg font-semibold mb-2\">{feature.title}</h3>\n              <p className=\"text-sm text-muted-foreground mb-4\">{feature.description}</p>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-sm\">\n                  <span className=\"text-muted-foreground\">Accuracy: </span>\n                  <span className=\"font-semibold text-green-600\">{feature.accuracy}</span>\n                </div>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setActiveFeature(feature.id)}\n                  className=\"group-hover:bg-blue-50 group-hover:text-blue-600\"\n                >\n                  <ChevronRight className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Feature Tabs */}\n      <Tabs value={activeFeature} onValueChange={setActiveFeature} className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-4 bg-white/50 backdrop-blur-sm border border-white/20 rounded-xl p-1\">\n          <TabsTrigger value=\"overview\" className=\"rounded-lg\">Overview</TabsTrigger>\n          <TabsTrigger value=\"face-recognition\" className=\"rounded-lg\">Face Recognition</TabsTrigger>\n          <TabsTrigger value=\"chatbot\" className=\"rounded-lg\">AI Chatbot</TabsTrigger>\n          <TabsTrigger value=\"predictive-analytics\" className=\"rounded-lg\">Predictive Analytics</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"overview\" className=\"space-y-6\">\n          <Card className=\"card-modern border-0\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Sparkles className=\"h-5 w-5 text-yellow-500\" />\n                <span>AI Features Overview</span>\n              </CardTitle>\n              <CardDescription>\n                Explore the cutting-edge AI capabilities integrated into your school management system\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-semibold\">🎯 Key Benefits</h3>\n                  <ul className=\"space-y-2 text-sm\">\n                    <li className=\"flex items-center space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full\"></div>\n                      <span>Automated attendance tracking with 96.8% accuracy</span>\n                    </li>\n                    <li className=\"flex items-center space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\n                      <span>24/7 AI assistant for instant query resolution</span>\n                    </li>\n                    <li className=\"flex items-center space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-purple-500 rounded-full\"></div>\n                      <span>Predictive insights for proactive interventions</span>\n                    </li>\n                    <li className=\"flex items-center space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-orange-500 rounded-full\"></div>\n                      <span>Reduced administrative workload by 60%</span>\n                    </li>\n                  </ul>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-semibold\">🚀 Quick Actions</h3>\n                  <div className=\"space-y-3\">\n                    <Button\n                      onClick={() => setActiveFeature('face-recognition')}\n                      className=\"w-full justify-start btn-gradient\"\n                    >\n                      <Camera className=\"h-4 w-4 mr-2\" />\n                      Start Face Recognition\n                    </Button>\n                    <Button\n                      onClick={toggleChatbot}\n                      className=\"w-full justify-start\"\n                      variant=\"outline\"\n                    >\n                      <Bot className=\"h-4 w-4 mr-2\" />\n                      Open AI Assistant\n                    </Button>\n                    <Button\n                      onClick={() => setActiveFeature('predictive-analytics')}\n                      className=\"w-full justify-start\"\n                      variant=\"outline\"\n                    >\n                      <TrendingUp className=\"h-4 w-4 mr-2\" />\n                      View Analytics\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"face-recognition\">\n          <FaceRecognitionAttendance onAttendanceMarked={handleAttendanceMarked} />\n        </TabsContent>\n\n        <TabsContent value=\"chatbot\" className=\"space-y-6\">\n          <Card className=\"card-modern border-0\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <MessageSquare className=\"h-5 w-5 text-green-600\" />\n                <span>AI Assistant Chatbot</span>\n              </CardTitle>\n              <CardDescription>\n                Intelligent chatbot powered by advanced NLP for answering queries and providing assistance\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-12\">\n                <Bot className=\"h-16 w-16 text-green-500 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold mb-2\">AI Assistant Ready</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Click the button below to start chatting with our AI assistant\n                </p>\n                <Button onClick={toggleChatbot} className=\"btn-gradient\">\n                  <MessageSquare className=\"h-4 w-4 mr-2\" />\n                  Open AI Chatbot\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"predictive-analytics\">\n          <PredictiveAnalytics />\n        </TabsContent>\n      </Tabs>\n\n      {/* AI Chatbot Component */}\n      {isChatbotOpen && (\n        <AIChatbot\n          userType=\"admin\"\n          isMinimized={isChatbotMinimized}\n          onToggleMinimize={toggleChatbotMinimize}\n          onClose={closeChatbot}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;AAuBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yMAAA,CAAA,SAAM;YACZ,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2NAAA,CAAA,gBAAa;YACnB,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qNAAA,CAAA,aAAU;YAChB,QAAQ;YACR,UAAU;YACV,OAAO;QACT;KACD;IAED,MAAM,yBAAyB,CAAC;QAC9B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,kBAAkB,CAAC;IAC5E;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAC;QAClB,sBAAsB;IACxB;IAEA,MAAM,wBAAwB;QAC5B,sBAAsB,CAAC;IACzB;IAEA,MAAM,eAAe;QACnB,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAIjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,wBACf,6LAAC,mIAAA,CAAA,OAAI;wBAAkB,WAAU;kCAC/B,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,KAAK,CAAC,8GAA8G,CAAC;sDAC1K,cAAA,6LAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAChC,QAAQ,MAAM;;;;;;;;;;;;8CAInB,6LAAC;oCAAG,WAAU;8CAA8B,QAAQ,KAAK;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAsC,QAAQ,WAAW;;;;;;8CAEtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAgC,QAAQ,QAAQ;;;;;;;;;;;;sDAElE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,iBAAiB,QAAQ,EAAE;4CAC1C,WAAU;sDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBAzBrB,QAAQ,EAAE;;;;;;;;;;0BAkCzB,6LAAC;gBAAK,OAAO;gBAAe,eAAe;gBAAkB,WAAU;;kCACrE,6LAAC;wBAAS,WAAU;;0CAClB,6LAAC;gCAAY,OAAM;gCAAW,WAAU;0CAAa;;;;;;0CACrD,6LAAC;gCAAY,OAAM;gCAAmB,WAAU;0CAAa;;;;;;0CAC7D,6LAAC;gCAAY,OAAM;gCAAU,WAAU;0CAAa;;;;;;0CACpD,6LAAC;gCAAY,OAAM;gCAAuB,WAAU;0CAAa;;;;;;;;;;;;kCAGnE,6LAAC;wBAAY,OAAM;wBAAW,WAAU;kCACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;0DAKZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,IAAM,iBAAiB;gEAChC,WAAU;;kFAEV,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGrC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS;gEACT,WAAU;gEACV,SAAQ;;kFAER,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,IAAM,iBAAiB;gEAChC,WAAU;gEACV,SAAQ;;kFAER,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUrD,6LAAC;wBAAY,OAAM;kCACjB,cAAA,6LAAC,wJAAA,CAAA,4BAAyB;4BAAC,oBAAoB;;;;;;;;;;;kCAGjD,6LAAC;wBAAY,OAAM;wBAAU,WAAU;kCACrC,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAe,WAAU;;kEACxC,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,6LAAC;wBAAY,OAAM;kCACjB,cAAA,6LAAC,kJAAA,CAAA,sBAAmB;;;;;;;;;;;;;;;;YAKvB,+BACC,6LAAC,wIAAA,CAAA,YAAS;gBACR,UAAS;gBACT,aAAa;gBACb,kBAAkB;gBAClB,SAAS;;;;;;;;;;;;AAKnB;GAtPwB;KAAA", "debugId": null}}]}