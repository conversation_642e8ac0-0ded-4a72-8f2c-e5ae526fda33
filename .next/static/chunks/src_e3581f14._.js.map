{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 hover:-translate-y-0.5\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-xl hover:from-red-600 hover:to-red-700 hover:-translate-y-0.5 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40\",\n        outline:\n          \"border-2 border-border bg-background shadow-md hover:bg-accent hover:text-accent-foreground hover:border-ring hover:shadow-lg dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 shadow-md hover:shadow-lg hover:from-gray-200 hover:to-gray-300 hover:-translate-y-0.5\",\n        ghost:\n          \"hover:bg-accent/50 hover:text-accent-foreground hover:shadow-sm backdrop-blur-sm dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline hover:text-primary/80\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5 text-xs\",\n        lg: \"h-12 rounded-xl px-8 has-[>svg]:px-6 text-base font-semibold\",\n        icon: \"size-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database helper functions\nexport const db = {\n  // Students\n  students: {\n    getAll: () => supabase.from('students').select('*'),\n    getById: (id: string) => supabase.from('students').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('students').insert(data),\n    update: (id: string, data: any) => supabase.from('students').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('students').delete().eq('id', id),\n  },\n\n  // Teachers\n  teachers: {\n    getAll: () => supabase.from('teachers').select('*'),\n    getById: (id: string) => supabase.from('teachers').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('teachers').insert(data),\n    update: (id: string, data: any) => supabase.from('teachers').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('teachers').delete().eq('id', id),\n  },\n\n  // Classes\n  classes: {\n    getAll: () => supabase.from('classes').select('*'),\n    getById: (id: string) => supabase.from('classes').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('classes').insert(data),\n    update: (id: string, data: any) => supabase.from('classes').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('classes').delete().eq('id', id),\n  },\n\n  // Attendance\n  attendance: {\n    getAll: () => supabase.from('attendance').select('*'),\n    getByDate: (date: string) => supabase.from('attendance').select('*').eq('date', date),\n    getByStudent: (studentId: string) => supabase.from('attendance').select('*').eq('student_id', studentId),\n    create: (data: any) => supabase.from('attendance').insert(data),\n    update: (id: string, data: any) => supabase.from('attendance').update(data).eq('id', id),\n  },\n\n  // Timetable\n  timetable: {\n    getAll: () => supabase.from('timetable').select('*'),\n    getByClass: (classId: string) => supabase.from('timetable').select('*').eq('class_id', classId),\n    create: (data: any) => supabase.from('timetable').insert(data),\n    update: (id: string, data: any) => supabase.from('timetable').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('timetable').delete().eq('id', id),\n  },\n\n  // Fees\n  fees: {\n    getAll: () => supabase.from('fees').select('*'),\n    getByStudent: (studentId: string) => supabase.from('fees').select('*').eq('student_id', studentId),\n    getPending: () => supabase.from('fees').select('*').eq('status', 'pending'),\n    create: (data: any) => supabase.from('fees').insert(data),\n    update: (id: string, data: any) => supabase.from('fees').update(data).eq('id', id),\n  },\n\n  // Notifications\n  notifications: {\n    getAll: () => supabase.from('notifications').select('*'),\n    getRecent: (limit: number = 10) => supabase.from('notifications').select('*').order('created_at', { ascending: false }).limit(limit),\n    create: (data: any) => supabase.from('notifications').insert(data),\n    markAsRead: (id: string, userId: string) => {\n      // This would need custom logic to update the read_by array\n      return supabase.rpc('mark_notification_read', { notification_id: id, user_id: userId });\n    },\n  },\n\n  // Leave Management\n  leaveRequests: {\n    getAll: () => supabase.from('leave_requests').select('*'),\n    getById: (id: string) => supabase.from('leave_requests').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('leave_requests').insert(data),\n    update: (id: string, data: any) => supabase.from('leave_requests').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('leave_requests').delete().eq('id', id),\n    getByUser: (userId: string) => supabase.from('leave_requests').select('*').eq('user_id', userId),\n    getByStatus: (status: string) => supabase.from('leave_requests').select('*').eq('status', status),\n    getPending: () => supabase.from('leave_requests').select('*').eq('status', 'pending'),\n  },\n\n  // Exam Management\n  exams: {\n    getAll: () => supabase.from('exams').select('*'),\n    getById: (id: string) => supabase.from('exams').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('exams').insert(data),\n    update: (id: string, data: any) => supabase.from('exams').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('exams').delete().eq('id', id),\n    getByClass: (classId: string) => supabase.from('exams').select('*').eq('class_id', classId),\n    getBySubject: (subjectId: string) => supabase.from('exams').select('*').eq('subject_id', subjectId),\n    getUpcoming: () => supabase.from('exams').select('*').gte('exam_date', new Date().toISOString().split('T')[0]),\n  },\n\n  // Grade Management\n  grades: {\n    getAll: () => supabase.from('grades').select('*'),\n    getById: (id: string) => supabase.from('grades').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('grades').insert(data),\n    update: (id: string, data: any) => supabase.from('grades').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('grades').delete().eq('id', id),\n    getByStudent: (studentId: string) => supabase.from('grades').select('*').eq('student_id', studentId),\n    getByExam: (examId: string) => supabase.from('grades').select('*').eq('exam_id', examId),\n    getStudentGrades: (studentId: string) => supabase.from('grades').select('*, exams(*)').eq('student_id', studentId),\n  },\n\n  // Event Management\n  events: {\n    getAll: () => supabase.from('events').select('*'),\n    getById: (id: string) => supabase.from('events').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('events').insert(data),\n    update: (id: string, data: any) => supabase.from('events').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('events').delete().eq('id', id),\n    getUpcoming: () => supabase.from('events').select('*').gte('start_date', new Date().toISOString().split('T')[0]),\n    getByDateRange: (startDate: string, endDate: string) => supabase.from('events').select('*').gte('start_date', startDate).lte('end_date', endDate),\n    getHolidays: () => supabase.from('events').select('*').eq('is_holiday', true),\n  },\n\n  // Announcement Management\n  announcements: {\n    getAll: () => supabase.from('announcements').select('*'),\n    getById: (id: string) => supabase.from('announcements').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('announcements').insert(data),\n    update: (id: string, data: any) => supabase.from('announcements').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('announcements').delete().eq('id', id),\n    getActive: () => supabase.from('announcements').select('*').or('expires_at.is.null,expires_at.gte.' + new Date().toISOString()),\n    getEmergency: () => supabase.from('announcements').select('*').eq('is_emergency', true),\n    getByAudience: (audience: string) => supabase.from('announcements').select('*').eq('target_audience', audience),\n  },\n\n  // Library Management\n  books: {\n    getAll: () => supabase.from('books').select('*'),\n    getById: (id: string) => supabase.from('books').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('books').insert(data),\n    update: (id: string, data: any) => supabase.from('books').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('books').delete().eq('id', id),\n    search: (query: string) => supabase.from('books').select('*').or(`title.ilike.%${query}%,author.ilike.%${query}%,isbn.ilike.%${query}%`),\n    getAvailable: () => supabase.from('books').select('*').gt('available_copies', 0),\n    getByCategory: (category: string) => supabase.from('books').select('*').eq('category', category),\n  },\n\n  bookTransactions: {\n    getAll: () => supabase.from('book_transactions').select('*'),\n    getById: (id: string) => supabase.from('book_transactions').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('book_transactions').insert(data),\n    update: (id: string, data: any) => supabase.from('book_transactions').update(data).eq('id', id),\n    delete: (id: string) => supabase.from('book_transactions').delete().eq('id', id),\n    getByUser: (userId: string) => supabase.from('book_transactions').select('*, books(*)').eq('user_id', userId),\n    getActive: () => supabase.from('book_transactions').select('*, books(*)').eq('status', 'active'),\n    getOverdue: () => supabase.from('book_transactions').select('*, books(*)').eq('status', 'overdue'),\n    getUserBorrowedBooks: (userId: string) => supabase.from('book_transactions').select('*, books(*)').eq('user_id', userId).eq('status', 'active'),\n  },\n};\n\n// Auth helper functions\nexport const auth = {\n  signIn: (email: string, password: string) => supabase.auth.signInWithPassword({ email, password }),\n  signOut: () => supabase.auth.signOut(),\n  getUser: () => supabase.auth.getUser(),\n  getSession: () => supabase.auth.getSession(),\n};\n"], "names": [], "mappings": ";;;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,KAAK;IAChB,WAAW;IACX,UAAU;QACR,QAAQ,IAAM,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC;QAC/C,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAClF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC;QACxD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QACnF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,MAAM;IACtE;IAEA,WAAW;IACX,UAAU;QACR,QAAQ,IAAM,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC;QAC/C,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAClF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC;QACxD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QACnF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,MAAM;IACtE;IAEA,UAAU;IACV,SAAS;QACP,QAAQ,IAAM,SAAS,IAAI,CAAC,WAAW,MAAM,CAAC;QAC9C,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,WAAW,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QACjF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,WAAW,MAAM,CAAC;QACvD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,WAAW,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QAClF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM;IACrE;IAEA,aAAa;IACb,YAAY;QACV,QAAQ,IAAM,SAAS,IAAI,CAAC,cAAc,MAAM,CAAC;QACjD,WAAW,CAAC,OAAiB,SAAS,IAAI,CAAC,cAAc,MAAM,CAAC,KAAK,EAAE,CAAC,QAAQ;QAChF,cAAc,CAAC,YAAsB,SAAS,IAAI,CAAC,cAAc,MAAM,CAAC,KAAK,EAAE,CAAC,cAAc;QAC9F,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,cAAc,MAAM,CAAC;QAC1D,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,cAAc,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;IACvF;IAEA,YAAY;IACZ,WAAW;QACT,QAAQ,IAAM,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC;QAChD,YAAY,CAAC,UAAoB,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,KAAK,EAAE,CAAC,YAAY;QACvF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC;QACzD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QACpF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,MAAM;IACvE;IAEA,OAAO;IACP,MAAM;QACJ,QAAQ,IAAM,SAAS,IAAI,CAAC,QAAQ,MAAM,CAAC;QAC3C,cAAc,CAAC,YAAsB,SAAS,IAAI,CAAC,QAAQ,MAAM,CAAC,KAAK,EAAE,CAAC,cAAc;QACxF,YAAY,IAAM,SAAS,IAAI,CAAC,QAAQ,MAAM,CAAC,KAAK,EAAE,CAAC,UAAU;QACjE,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,QAAQ,MAAM,CAAC;QACpD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;IACjF;IAEA,gBAAgB;IAChB,eAAe;QACb,QAAQ,IAAM,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC;QACpD,WAAW,CAAC,QAAgB,EAAE,GAAK,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC,KAAK,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GAAG,KAAK,CAAC;QAC9H,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC;QAC7D,YAAY,CAAC,IAAY;YACvB,2DAA2D;YAC3D,OAAO,SAAS,GAAG,CAAC,0BAA0B;gBAAE,iBAAiB;gBAAI,SAAS;YAAO;QACvF;IACF;IAEA,mBAAmB;IACnB,eAAe;QACb,QAAQ,IAAM,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC;QACrD,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QACxF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC;QAC9D,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QACzF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,kBAAkB,MAAM,GAAG,EAAE,CAAC,MAAM;QAC1E,WAAW,CAAC,SAAmB,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC,KAAK,EAAE,CAAC,WAAW;QACzF,aAAa,CAAC,SAAmB,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC,KAAK,EAAE,CAAC,UAAU;QAC1F,YAAY,IAAM,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC,KAAK,EAAE,CAAC,UAAU;IAC7E;IAEA,kBAAkB;IAClB,OAAO;QACL,QAAQ,IAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;QAC5C,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAC/E,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;QACrD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QAChF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM;QACjE,YAAY,CAAC,UAAoB,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,YAAY;QACnF,cAAc,CAAC,YAAsB,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,cAAc;QACzF,aAAa,IAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,GAAG,CAAC,aAAa,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC/G;IAEA,mBAAmB;IACnB,QAAQ;QACN,QAAQ,IAAM,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC;QAC7C,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAChF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC;QACtD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QACjF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,UAAU,MAAM,GAAG,EAAE,CAAC,MAAM;QAClE,cAAc,CAAC,YAAsB,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,cAAc;QAC1F,WAAW,CAAC,SAAmB,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,WAAW;QACjF,kBAAkB,CAAC,YAAsB,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,eAAe,EAAE,CAAC,cAAc;IAC1G;IAEA,mBAAmB;IACnB,QAAQ;QACN,QAAQ,IAAM,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC;QAC7C,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAChF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC;QACtD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QACjF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,UAAU,MAAM,GAAG,EAAE,CAAC,MAAM;QAClE,aAAa,IAAM,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,GAAG,CAAC,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/G,gBAAgB,CAAC,WAAmB,UAAoB,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,GAAG,CAAC,cAAc,WAAW,GAAG,CAAC,YAAY;QACzI,aAAa,IAAM,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE,CAAC,cAAc;IAC1E;IAEA,0BAA0B;IAC1B,eAAe;QACb,QAAQ,IAAM,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC;QACpD,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QACvF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC;QAC7D,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QACxF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,iBAAiB,MAAM,GAAG,EAAE,CAAC,MAAM;QACzE,WAAW,IAAM,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC,KAAK,EAAE,CAAC,uCAAuC,IAAI,OAAO,WAAW;QAC5H,cAAc,IAAM,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC,KAAK,EAAE,CAAC,gBAAgB;QAClF,eAAe,CAAC,WAAqB,SAAS,IAAI,CAAC,iBAAiB,MAAM,CAAC,KAAK,EAAE,CAAC,mBAAmB;IACxG;IAEA,qBAAqB;IACrB,OAAO;QACL,QAAQ,IAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;QAC5C,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAC/E,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;QACrD,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QAChF,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM;QACjE,QAAQ,CAAC,QAAkB,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,aAAa,EAAE,MAAM,gBAAgB,EAAE,MAAM,cAAc,EAAE,MAAM,CAAC,CAAC;QACvI,cAAc,IAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,oBAAoB;QAC9E,eAAe,CAAC,WAAqB,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,YAAY;IACzF;IAEA,kBAAkB;QAChB,QAAQ,IAAM,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC;QACxD,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAC3F,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC;QACjE,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM;QAC5F,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,qBAAqB,MAAM,GAAG,EAAE,CAAC,MAAM;QAC7E,WAAW,CAAC,SAAmB,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,eAAe,EAAE,CAAC,WAAW;QACtG,WAAW,IAAM,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,eAAe,EAAE,CAAC,UAAU;QACvF,YAAY,IAAM,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,eAAe,EAAE,CAAC,UAAU;QACxF,sBAAsB,CAAC,SAAmB,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,eAAe,EAAE,CAAC,WAAW,QAAQ,EAAE,CAAC,UAAU;IACxI;AACF;AAGO,MAAM,OAAO;IAClB,QAAQ,CAAC,OAAe,WAAqB,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAAE;YAAO;QAAS;IAChG,SAAS,IAAM,SAAS,IAAI,CAAC,OAAO;IACpC,SAAS,IAAM,SAAS,IAAI,CAAC,OAAO;IACpC,YAAY,IAAM,SAAS,IAAI,CAAC,UAAU;AAC5C", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  Users,\n  GraduationCap,\n  BookOpen,\n  Calendar,\n  DollarSign,\n  Bell,\n  Settings,\n  LogOut,\n  School,\n  FileText,\n  ClipboardList,\n  CalendarDays,\n  Megaphone,\n  Book,\n  BarChart3,\n} from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { auth } from '@/lib/supabase';\nimport { toast } from 'sonner';\nimport { useRouter } from 'next/navigation';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Students', href: '/dashboard/students', icon: Users },\n  { name: 'Teachers', href: '/dashboard/teachers', icon: GraduationCap },\n  { name: 'Classes', href: '/dashboard/classes', icon: BookOpen },\n  { name: 'Attendance', href: '/dashboard/attendance', icon: Calendar },\n  { name: 'Timetable', href: '/dashboard/timetable', icon: Calendar },\n  { name: 'Leave Management', href: '/dashboard/leave-management', icon: FileText },\n  { name: 'Exams', href: '/dashboard/exams', icon: ClipboardList },\n  { name: 'Events', href: '/dashboard/events', icon: CalendarDays },\n  { name: 'Announcements', href: '/dashboard/announcements', icon: Megaphone },\n  { name: 'Library', href: '/dashboard/library', icon: Book },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 },\n  { name: 'Fees', href: '/dashboard/fees', icon: DollarSign },\n  { name: 'Notifications', href: '/dashboard/notifications', icon: Bell },\n  { name: 'Settings', href: '/dashboard/settings', icon: Settings },\n];\n\nexport function Sidebar() {\n  const pathname = usePathname();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      // Demo mode - skip actual logout\n      if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {\n        toast.success('Logged out successfully');\n        router.push('/login');\n        return;\n      }\n\n      // Real Supabase logout\n      await auth.signOut();\n      toast.success('Logged out successfully');\n      router.push('/login');\n    } catch {\n      toast.error('Error logging out');\n    }\n  };\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-white border-r border-gray-200\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n        <School className=\"h-8 w-8 text-blue-600\" />\n        <span className=\"ml-2 text-xl font-semibold text-gray-900\">\n          School Admin\n        </span>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href;\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                isActive\n                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-500'\n                )}\n              />\n              {item.name}\n            </Link>\n          );\n        })}\n      </nav>\n\n      {/* Logout Button */}\n      <div className=\"p-3 border-t border-gray-200\">\n        <Button\n          variant=\"ghost\"\n          className=\"w-full justify-start text-gray-700 hover:bg-gray-50\"\n          onClick={handleLogout}\n        >\n          <LogOut className=\"mr-3 h-5 w-5\" />\n          Logout\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAqDU;;AAnDV;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AACA;;;AAzBA;;;;;;;;;AA4BA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,uMAAA,CAAA,QAAK;IAAC;IAC7D;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,2NAAA,CAAA,gBAAa;IAAC;IACrE;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC9D;QAAE,MAAM;QAAc,MAAM;QAAyB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACpE;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAClE;QAAE,MAAM;QAAoB,MAAM;QAA+B,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAChF;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,2NAAA,CAAA,gBAAa;IAAC;IAC/D;QAAE,MAAM;QAAU,MAAM;QAAqB,MAAM,yNAAA,CAAA,eAAY;IAAC;IAChE;QAAE,MAAM;QAAiB,MAAM;QAA4B,MAAM,+MAAA,CAAA,YAAS;IAAC;IAC3E;QAAE,MAAM;QAAW,MAAM;QAAsB,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC1D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,qNAAA,CAAA,YAAS;IAAC;IACnE;QAAE,MAAM;QAAQ,MAAM;QAAmB,MAAM,qNAAA,CAAA,aAAU;IAAC;IAC1D;QAAE,MAAM;QAAiB,MAAM;QAA4B,MAAM,qMAAA,CAAA,OAAI;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACjE;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,iCAAiC;YACjC,wCAAkD;gBAChD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;gBACZ;YACF;;QAMF,EAAE,OAAM;YACN,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAK,WAAU;kCAA2C;;;;;;;;;;;;0BAM7D,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,wDACA;;0CAGN,6LAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,kBAAkB;;;;;;4BAGhC,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;oBACV,SAAS;;sCAET,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM7C;GAxEgB;;QACG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Sidebar } from '@/components/layout/sidebar';\nimport { auth } from '@/lib/supabase';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [loading, setLoading] = useState(true);\n  const [authenticated, setAuthenticated] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        // Demo mode - skip authentication check\n        if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {\n          setAuthenticated(true);\n          setLoading(false);\n          return;\n        }\n\n        // Real Supabase authentication check\n        const { data: { session } } = await auth.getSession();\n\n        if (!session) {\n          router.push('/login');\n          return;\n        }\n\n        setAuthenticated(true);\n      } catch (error) {\n        router.push('/login');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!authenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"h-screen flex bg-gray-50\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto\">\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAoBY;;AAlBZ;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;uDAAY;oBAChB,IAAI;wBACF,wCAAwC;wBACxC,wCAAkD;4BAChD,iBAAiB;4BACjB,WAAW;4BACX;wBACF;;wBAEA,qCAAqC;wBACrC,MAAgB;oBAQlB,EAAE,OAAO,OAAO;wBACd,OAAO,IAAI,CAAC;oBACd,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;oCAAG;QAAC;KAAO;IAEX,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GA5DwB;;QAOP,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}]}