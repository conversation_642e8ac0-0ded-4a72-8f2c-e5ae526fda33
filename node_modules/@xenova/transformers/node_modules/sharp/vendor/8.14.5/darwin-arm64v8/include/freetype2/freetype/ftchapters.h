/****************************************************************************
 *
 * This file defines the structure of the FreeType reference.
 * It is used by the python script that generates the HTML files.
 *
 */


  /**************************************************************************
   *
   * @chapter:
   *   general_remarks
   *
   * @title:
   *   General Remarks
   *
   * @sections:
   *   preamble
   *   header_inclusion
   *   user_allocation
   *
   */


  /**************************************************************************
   *
   * @chapter:
   *   core_api
   *
   * @title:
   *   Core API
   *
   * @sections:
   *   basic_types
   *   library_setup
   *   face_creation
   *   font_testing_macros
   *   sizing_and_scaling
   *   glyph_retrieval
   *   character_mapping
   *   information_retrieval
   *   other_api_data
   *
   */


  /**************************************************************************
   *
   * @chapter:
   *   extended_api
   *
   * @title:
   *   Extended API
   *
   * @sections:
   *   glyph_variants
   *   color_management
   *   layer_management
   *   glyph_management
   *   mac_specific
   *   sizes_management
   *   header_file_macros
   *
   */


  /**************************************************************************
   *
   * @chapter:
   *   format_specific
   *
   * @title:
   *   Format-Specific API
   *
   * @sections:
   *   multiple_masters
   *   truetype_tables
   *   type1_tables
   *   sfnt_names
   *   bdf_fonts
   *   cid_fonts
   *   pfr_fonts
   *   winfnt_fonts
   *   svg_fonts
   *   font_formats
   *   gasp_table
   *
   */


  /**************************************************************************
   *
   * @chapter:
   *   module_specific
   *
   * @title:
   *   Controlling FreeType Modules
   *
   * @sections:
   *   auto_hinter
   *   cff_driver
   *   t1_cid_driver
   *   tt_driver
   *   pcf_driver
   *   ot_svg_driver
   *   properties
   *   parameter_tags
   *   lcd_rendering
   *
   */


  /**************************************************************************
   *
   * @chapter:
   *   cache_subsystem
   *
   * @title:
   *   Cache Sub-System
   *
   * @sections:
   *   cache_subsystem
   *
   */


  /**************************************************************************
   *
   * @chapter:
   *   support_api
   *
   * @title:
   *   Support API
   *
   * @sections:
   *   computations
   *   list_processing
   *   outline_processing
   *   quick_advance
   *   bitmap_handling
   *   raster
   *   glyph_stroker
   *   system_interface
   *   module_management
   *   gzip
   *   lzw
   *   bzip2
   *   debugging_apis
   *
   */


  /**************************************************************************
   *
   * @chapter:
   *   error_codes
   *
   * @title:
   *   Error Codes
   *
   * @sections:
   *   error_enumerations
   *   error_code_values
   *
   */


/* END */
