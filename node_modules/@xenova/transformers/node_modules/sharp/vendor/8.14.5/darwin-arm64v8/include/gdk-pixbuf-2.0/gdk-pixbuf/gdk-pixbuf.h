/* GdkPixbuf library - Main header file
 *
 * Copyright (C) 1999 The Free Software Foundation
 *
 * Authors: <AUTHORS>
 *          <PERSON> <<EMAIL>>
 *          <PERSON> <<EMAIL>>
 *          Havoc Pennington <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#ifndef GDK_PIXBUF_H
#define GDK_PIXBUF_H

#define GDK_PIXBUF_H_INSIDE

#include <glib.h>
#include <gdk-pixbuf/gdk-pixbuf-macros.h>
#include <gdk-pixbuf/gdk-pixbuf-features.h>
#include <glib-object.h>

#include <gdk-pixbuf/gdk-pixbuf-core.h>
#include <gdk-pixbuf/gdk-pixbuf-transform.h>
#include <gdk-pixbuf/gdk-pixbuf-animation.h>
#include <gdk-pixbuf/gdk-pixbuf-simple-anim.h>
#include <gdk-pixbuf/gdk-pixbuf-io.h>
#include <gdk-pixbuf/gdk-pixbuf-loader.h>
#include <gdk-pixbuf/gdk-pixbuf-enum-types.h>

#include <gdk-pixbuf/gdk-pixbuf-autocleanups.h>

#undef GDK_PIXBUF_H_INSIDE

#endif  /* GDK_PIXBUF_H */
