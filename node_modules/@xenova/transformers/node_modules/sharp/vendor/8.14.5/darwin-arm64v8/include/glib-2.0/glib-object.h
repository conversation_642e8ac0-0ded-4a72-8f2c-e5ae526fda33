/* GObject - GLib Type, Object, Parameter and Signal Library
 * Copyright (C) 1998, 1999, 2000 <PERSON> and Red Hat, Inc.
 *
 * SPDX-License-Identifier: LGPL-2.1-or-later
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.	 See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General
 * Public License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */
#ifndef __GLIB_GOBJECT_H__
#define __GLIB_GOBJECT_H__

#define __GLIB_GOBJECT_H_INSIDE__

#include <gobject/gbinding.h>
#include <gobject/gbindinggroup.h>
#include <gobject/gboxed.h>
#include <gobject/genums.h>
#include <gobject/glib-enumtypes.h>
#include <gobject/gobject.h>
#include <gobject/gparam.h>
#include <gobject/gparamspecs.h>
#include <gobject/gsignal.h>
#include <gobject/gsignalgroup.h>
#include <gobject/gsourceclosure.h>
#include <gobject/gtype.h>
#include <gobject/gtypemodule.h>
#include <gobject/gtypeplugin.h>
#include <gobject/gvaluearray.h>
#include <gobject/gvalue.h>
#include <gobject/gvaluetypes.h>

#include <gobject/gobject-autocleanups.h>

#undef __GLIB_GOBJECT_H_INSIDE__

#endif /* __GLIB_GOBJECT_H__ */
