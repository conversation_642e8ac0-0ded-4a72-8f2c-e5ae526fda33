'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Brain,
  Camera,
  MessageSquare,
  TrendingUp,
  Zap,
  Users,
  Bot,
  Target,
  Sparkles,
  ChevronRight
} from 'lucide-react';
import { FaceRecognitionAttendance } from '@/components/ai/FaceRecognitionAttendance';
import { AIChatbot } from '@/components/ai/AIChatbot';
import { PredictiveAnalytics } from '@/components/ai/PredictiveAnalytics';
// import { toast } from 'sonner';

// Mock toast function for demo
const toast = {
  success: (message: string) => console.log('Success:', message),
  error: (message: string) => console.log('Error:', message),
  info: (message: string) => console.log('Info:', message),
};

export default function AIFeaturesPage() {
  const [activeFeature, setActiveFeature] = useState<string>('overview');
  const [isChatbotOpen, setIsChatbotOpen] = useState(false);
  const [isChatbotMinimized, setIsChatbotMinimized] = useState(false);

  const aiFeatures = [
    {
      id: 'face-recognition',
      title: 'Face Recognition Attendance',
      description: 'AI-powered automatic attendance marking using facial recognition technology',
      icon: Camera,
      status: 'active',
      accuracy: '96.8%',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'chatbot',
      title: 'AI Assistant Chatbot',
      description: 'Intelligent chatbot for answering student, parent, and teacher queries',
      icon: MessageSquare,
      status: 'active',
      accuracy: '94.2%',
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'predictive-analytics',
      title: 'Predictive Analytics',
      description: 'Advanced ML models for predicting student outcomes and risks',
      icon: TrendingUp,
      status: 'active',
      accuracy: '91.5%',
      color: 'from-purple-500 to-purple-600'
    }
  ];

  const handleAttendanceMarked = (students: any[]) => {
    toast.success(`Attendance marked for ${students.length} students using AI`);
  };

  const toggleChatbot = () => {
    setIsChatbotOpen(!isChatbotOpen);
    setIsChatbotMinimized(false);
  };

  const toggleChatbotMinimize = () => {
    setIsChatbotMinimized(!isChatbotMinimized);
  };

  const closeChatbot = () => {
    setIsChatbotOpen(false);
    setIsChatbotMinimized(false);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-3xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-xl">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Brain className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold gradient-text">AI-Powered Features</h1>
              <p className="text-muted-foreground text-lg">Advanced artificial intelligence for modern education management</p>
            </div>
          </div>

          <div className="flex items-center space-x-6 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>All AI Models Online</span>
            </div>
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <span>Real-time Processing</span>
            </div>
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-blue-500" />
              <span>94.2% Average Accuracy</span>
            </div>
          </div>
        </div>
      </div>

      {/* AI Features Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {aiFeatures.map((feature) => (
          <Card key={feature.id} className="card-modern border-0 hover:shadow-2xl transition-all duration-300 group cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200`}>
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <Badge variant="default" className="bg-green-100 text-green-700">
                  {feature.status}
                </Badge>
              </div>

              <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
              <p className="text-sm text-muted-foreground mb-4">{feature.description}</p>

              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <span className="text-muted-foreground">Accuracy: </span>
                  <span className="font-semibold text-green-600">{feature.accuracy}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveFeature(feature.id)}
                  className="group-hover:bg-blue-50 group-hover:text-blue-600"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Feature Tabs */}
      <Tabs value={activeFeature} onValueChange={setActiveFeature} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white/50 backdrop-blur-sm border border-white/20 rounded-xl p-1">
          <TabsTrigger value="overview" className="rounded-lg">Overview</TabsTrigger>
          <TabsTrigger value="face-recognition" className="rounded-lg">Face Recognition</TabsTrigger>
          <TabsTrigger value="chatbot" className="rounded-lg">AI Chatbot</TabsTrigger>
          <TabsTrigger value="predictive-analytics" className="rounded-lg">Predictive Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card className="card-modern border-0">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5 text-yellow-500" />
                <span>AI Features Overview</span>
              </CardTitle>
              <CardDescription>
                Explore the cutting-edge AI capabilities integrated into your school management system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">🎯 Key Benefits</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                      <span>Automated attendance tracking with 96.8% accuracy</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                      <span>24/7 AI assistant for instant query resolution</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                      <span>Predictive insights for proactive interventions</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                      <span>Reduced administrative workload by 60%</span>
                    </li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">🚀 Quick Actions</h3>
                  <div className="space-y-3">
                    <Button
                      onClick={() => setActiveFeature('face-recognition')}
                      className="w-full justify-start btn-gradient"
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      Start Face Recognition
                    </Button>
                    <Button
                      onClick={toggleChatbot}
                      className="w-full justify-start"
                      variant="outline"
                    >
                      <Bot className="h-4 w-4 mr-2" />
                      Open AI Assistant
                    </Button>
                    <Button
                      onClick={() => setActiveFeature('predictive-analytics')}
                      className="w-full justify-start"
                      variant="outline"
                    >
                      <TrendingUp className="h-4 w-4 mr-2" />
                      View Analytics
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="face-recognition">
          <FaceRecognitionAttendance onAttendanceMarked={handleAttendanceMarked} />
        </TabsContent>

        <TabsContent value="chatbot" className="space-y-6">
          <Card className="card-modern border-0">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-green-600" />
                <span>AI Assistant Chatbot</span>
              </CardTitle>
              <CardDescription>
                Intelligent chatbot powered by advanced NLP for answering queries and providing assistance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Bot className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">AI Assistant Ready</h3>
                <p className="text-muted-foreground mb-6">
                  Click the button below to start chatting with our AI assistant
                </p>
                <Button onClick={toggleChatbot} className="btn-gradient">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Open AI Chatbot
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="predictive-analytics">
          <PredictiveAnalytics />
        </TabsContent>
      </Tabs>

      {/* AI Chatbot Component */}
      {isChatbotOpen && (
        <AIChatbot
          userType="admin"
          isMinimized={isChatbotMinimized}
          onToggleMinimize={toggleChatbotMinimize}
          onClose={closeChatbot}
        />
      )}
    </div>
  );
}
