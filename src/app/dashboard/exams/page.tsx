'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, Plus, BookOpen, Users, Edit, Trash2, GraduationCap } from 'lucide-react';
import { toast } from 'sonner';
import { Exam, ExamFormData } from '@/lib/types';

// Mock data for exams
const mockExams: Exam[] = [
  {
    id: '1',
    name: 'Mathematics Mid-Term Exam',
    subject_id: 'math-101',
    class_id: 'grade-5-a',
    exam_date: '2024-02-15',
    start_time: '09:00',
    end_time: '11:00',
    total_marks: 100,
    passing_marks: 40,
    exam_type: 'mid_term',
    instructions: 'Bring calculator and geometry box. No mobile phones allowed.',
    created_by: 'admin-1',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: 'Science Unit Test',
    subject_id: 'science-101',
    class_id: 'grade-5-a',
    exam_date: '2024-02-20',
    start_time: '10:00',
    end_time: '11:30',
    total_marks: 50,
    passing_marks: 20,
    exam_type: 'unit_test',
    instructions: 'Chapter 1-3 will be covered.',
    created_by: 'admin-1',
    created_at: '2024-01-16T10:00:00Z',
    updated_at: '2024-01-16T10:00:00Z',
  },
  {
    id: '3',
    name: 'English Final Exam',
    subject_id: 'english-101',
    class_id: 'grade-5-b',
    exam_date: '2024-03-10',
    start_time: '09:00',
    end_time: '12:00',
    total_marks: 100,
    passing_marks: 35,
    exam_type: 'final',
    instructions: 'Comprehensive exam covering all chapters.',
    created_by: 'admin-1',
    created_at: '2024-01-17T10:00:00Z',
    updated_at: '2024-01-17T10:00:00Z',
  },
];

// Mock subjects and classes
const mockSubjects = [
  { id: 'math-101', name: 'Mathematics' },
  { id: 'science-101', name: 'Science' },
  { id: 'english-101', name: 'English' },
  { id: 'history-101', name: 'History' },
];

const mockClasses = [
  { id: 'grade-5-a', name: 'Grade 5-A' },
  { id: 'grade-5-b', name: 'Grade 5-B' },
  { id: 'grade-6-a', name: 'Grade 6-A' },
];

const getExamTypeColor = (type: string) => {
  switch (type) {
    case 'unit_test':
      return 'bg-blue-100 text-blue-800';
    case 'mid_term':
      return 'bg-yellow-100 text-yellow-800';
    case 'final':
      return 'bg-red-100 text-red-800';
    case 'practical':
      return 'bg-green-100 text-green-800';
    case 'assignment':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function ExamsPage() {
  const [exams, setExams] = useState<Exam[]>(mockExams);
  const [filteredExams, setFilteredExams] = useState<Exam[]>(mockExams);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingExam, setEditingExam] = useState<Exam | null>(null);
  const [filterClass, setFilterClass] = useState<string>('all');
  const [filterSubject, setFilterSubject] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [formData, setFormData] = useState<ExamFormData>({
    name: '',
    subject_id: '',
    class_id: '',
    exam_date: '',
    start_time: '',
    end_time: '',
    total_marks: 100,
    passing_marks: 40,
    exam_type: 'unit_test',
    instructions: '',
  });

  useEffect(() => {
    let filtered = exams;

    if (filterClass !== 'all') {
      filtered = filtered.filter(exam => exam.class_id === filterClass);
    }

    if (filterSubject !== 'all') {
      filtered = filtered.filter(exam => exam.subject_id === filterSubject);
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(exam => exam.exam_type === filterType);
    }

    setFilteredExams(filtered);
  }, [exams, filterClass, filterSubject, filterType]);

  const resetForm = () => {
    setFormData({
      name: '',
      subject_id: '',
      class_id: '',
      exam_date: '',
      start_time: '',
      end_time: '',
      total_marks: 100,
      passing_marks: 40,
      exam_type: 'unit_test',
      instructions: '',
    });
    setEditingExam(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.subject_id || !formData.class_id || !formData.exam_date) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.passing_marks >= formData.total_marks) {
      toast.error('Passing marks must be less than total marks');
      return;
    }

    if (editingExam) {
      // Update existing exam
      setExams(prev => prev.map(exam => 
        exam.id === editingExam.id 
          ? { ...exam, ...formData, updated_at: new Date().toISOString() }
          : exam
      ));
      toast.success('Exam updated successfully');
    } else {
      // Add new exam
      const newExam: Exam = {
        id: Date.now().toString(),
        ...formData,
        created_by: 'admin-1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      setExams(prev => [...prev, newExam]);
      toast.success('Exam created successfully');
    }

    setIsDialogOpen(false);
    resetForm();
  };

  const handleEdit = (exam: Exam) => {
    setEditingExam(exam);
    setFormData({
      name: exam.name,
      subject_id: exam.subject_id,
      class_id: exam.class_id,
      exam_date: exam.exam_date,
      start_time: exam.start_time,
      end_time: exam.end_time,
      total_marks: exam.total_marks,
      passing_marks: exam.passing_marks,
      exam_type: exam.exam_type,
      instructions: exam.instructions || '',
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (examId: string) => {
    setExams(prev => prev.filter(exam => exam.id !== examId));
    toast.success('Exam deleted successfully');
  };

  const getSubjectName = (subjectId: string) => {
    return mockSubjects.find(s => s.id === subjectId)?.name || subjectId;
  };

  const getClassName = (classId: string) => {
    return mockClasses.find(c => c.id === classId)?.name || classId;
  };

  const upcomingExams = exams.filter(exam => new Date(exam.exam_date) >= new Date());
  const pastExams = exams.filter(exam => new Date(exam.exam_date) < new Date());

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Exam Management</h1>
          <p className="text-gray-600">Create and manage exam schedules</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Create Exam
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{editingExam ? 'Edit Exam' : 'Create New Exam'}</DialogTitle>
              <DialogDescription>
                {editingExam ? 'Update exam details' : 'Set up a new exam schedule'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name">Exam Name</Label>
                <Input
                  id="name"
                  placeholder="e.g., Mathematics Mid-Term Exam"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="subject_id">Subject</Label>
                  <Select
                    value={formData.subject_id}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, subject_id: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockSubjects.map(subject => (
                        <SelectItem key={subject.id} value={subject.id}>
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="class_id">Class</Label>
                  <Select
                    value={formData.class_id}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, class_id: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select class" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockClasses.map(cls => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="exam_date">Exam Date</Label>
                  <Input
                    id="exam_date"
                    type="date"
                    value={formData.exam_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, exam_date: e.target.value }))}
                    min={new Date().toISOString().split('T')[0]}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="start_time">Start Time</Label>
                  <Input
                    id="start_time"
                    type="time"
                    value={formData.start_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="end_time">End Time</Label>
                  <Input
                    id="end_time"
                    type="time"
                    value={formData.end_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="exam_type">Exam Type</Label>
                  <Select
                    value={formData.exam_type}
                    onValueChange={(value: any) => setFormData(prev => ({ ...prev, exam_type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unit_test">Unit Test</SelectItem>
                      <SelectItem value="mid_term">Mid Term</SelectItem>
                      <SelectItem value="final">Final Exam</SelectItem>
                      <SelectItem value="practical">Practical</SelectItem>
                      <SelectItem value="assignment">Assignment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="total_marks">Total Marks</Label>
                  <Input
                    id="total_marks"
                    type="number"
                    min="1"
                    value={formData.total_marks}
                    onChange={(e) => setFormData(prev => ({ ...prev, total_marks: parseInt(e.target.value) }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="passing_marks">Passing Marks</Label>
                  <Input
                    id="passing_marks"
                    type="number"
                    min="1"
                    max={formData.total_marks - 1}
                    value={formData.passing_marks}
                    onChange={(e) => setFormData(prev => ({ ...prev, passing_marks: parseInt(e.target.value) }))}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="instructions">Instructions (Optional)</Label>
                <Textarea
                  id="instructions"
                  placeholder="Special instructions for students..."
                  value={formData.instructions}
                  onChange={(e) => setFormData(prev => ({ ...prev, instructions: e.target.value }))}
                />
              </div>
              <div className="flex space-x-2">
                <Button type="submit" className="flex-1">
                  {editingExam ? 'Update Exam' : 'Create Exam'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Exams</p>
                <p className="text-2xl font-bold text-gray-900">{exams.length}</p>
              </div>
              <BookOpen className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Upcoming</p>
                <p className="text-2xl font-bold text-green-600">{upcomingExams.length}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-600">{pastExams.length}</p>
              </div>
              <GraduationCap className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Classes</p>
                <p className="text-2xl font-bold text-purple-600">{mockClasses.length}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="filterClass">Class</Label>
              <Select value={filterClass} onValueChange={setFilterClass}>
                <SelectTrigger>
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {mockClasses.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="filterSubject">Subject</Label>
              <Select value={filterSubject} onValueChange={setFilterSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {mockSubjects.map(subject => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="filterType">Exam Type</Label>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="unit_test">Unit Test</SelectItem>
                  <SelectItem value="mid_term">Mid Term</SelectItem>
                  <SelectItem value="final">Final Exam</SelectItem>
                  <SelectItem value="practical">Practical</SelectItem>
                  <SelectItem value="assignment">Assignment</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exams List */}
      <Tabs defaultValue="upcoming" className="space-y-4">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming Exams</TabsTrigger>
          <TabsTrigger value="past">Past Exams</TabsTrigger>
          <TabsTrigger value="all">All Exams</TabsTrigger>
        </TabsList>
        
        <TabsContent value="upcoming">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Exams</CardTitle>
              <CardDescription>
                {upcomingExams.length} upcoming exam(s)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingExams.map((exam) => (
                  <div key={exam.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="font-semibold text-gray-900">{exam.name}</h3>
                          <Badge className={getExamTypeColor(exam.exam_type)}>
                            {exam.exam_type.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <span className="flex items-center">
                            <BookOpen className="h-4 w-4 mr-1" />
                            {getSubjectName(exam.subject_id)}
                          </span>
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {getClassName(exam.class_id)}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {exam.exam_date}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {exam.start_time} - {exam.end_time}
                          </span>
                        </div>
                        <div className="mt-2 text-sm text-gray-600">
                          <span>Total Marks: {exam.total_marks} | Passing: {exam.passing_marks}</span>
                        </div>
                        {exam.instructions && (
                          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded">
                            <p className="text-sm text-blue-800">{exam.instructions}</p>
                          </div>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(exam)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(exam.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                {upcomingExams.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No upcoming exams scheduled.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="past">
          <Card>
            <CardHeader>
              <CardTitle>Past Exams</CardTitle>
              <CardDescription>
                {pastExams.length} completed exam(s)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pastExams.map((exam) => (
                  <div key={exam.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="font-semibold text-gray-700">{exam.name}</h3>
                          <Badge variant="outline" className="opacity-75">
                            {exam.exam_type.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <BookOpen className="h-4 w-4 mr-1" />
                            {getSubjectName(exam.subject_id)}
                          </span>
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {getClassName(exam.class_id)}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {exam.exam_date}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {exam.start_time} - {exam.end_time}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {pastExams.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No past exams found.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>All Exams</CardTitle>
              <CardDescription>
                {filteredExams.length} exam(s) found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredExams.map((exam) => (
                  <div key={exam.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="font-semibold text-gray-900">{exam.name}</h3>
                          <Badge className={getExamTypeColor(exam.exam_type)}>
                            {exam.exam_type.replace('_', ' ').toUpperCase()}
                          </Badge>
                          {new Date(exam.exam_date) < new Date() && (
                            <Badge variant="outline">Completed</Badge>
                          )}
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <span className="flex items-center">
                            <BookOpen className="h-4 w-4 mr-1" />
                            {getSubjectName(exam.subject_id)}
                          </span>
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {getClassName(exam.class_id)}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {exam.exam_date}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {exam.start_time} - {exam.end_time}
                          </span>
                        </div>
                        <div className="mt-2 text-sm text-gray-600">
                          <span>Total Marks: {exam.total_marks} | Passing: {exam.passing_marks}</span>
                        </div>
                        {exam.instructions && (
                          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded">
                            <p className="text-sm text-blue-800">{exam.instructions}</p>
                          </div>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(exam)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(exam.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                {filteredExams.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No exams found matching your criteria.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
