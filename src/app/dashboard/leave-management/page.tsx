'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Calendar, Clock, User, FileText, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { LeaveRequest } from '@/lib/types';

// Mock data for demo
const mockLeaveRequests: LeaveRequest[] = [
  {
    id: '1',
    user_id: 'teacher-1',
    user_type: 'teacher',
    leave_type: 'sick',
    start_date: '2024-01-15',
    end_date: '2024-01-17',
    reason: 'Flu symptoms and need rest for recovery',
    status: 'pending',
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-10T10:00:00Z',
  },
  {
    id: '2',
    user_id: 'student-1',
    user_type: 'student',
    leave_type: 'family',
    start_date: '2024-01-20',
    end_date: '2024-01-22',
    reason: 'Family wedding ceremony',
    status: 'approved',
    approved_by: 'admin-1',
    approval_date: '2024-01-12T14:30:00Z',
    created_at: '2024-01-11T09:00:00Z',
    updated_at: '2024-01-12T14:30:00Z',
  },
  {
    id: '3',
    user_id: 'teacher-2',
    user_type: 'teacher',
    leave_type: 'personal',
    start_date: '2024-01-25',
    end_date: '2024-01-25',
    reason: 'Personal appointment',
    status: 'rejected',
    approved_by: 'admin-1',
    rejection_reason: 'Insufficient notice period',
    created_at: '2024-01-24T16:00:00Z',
    updated_at: '2024-01-24T18:00:00Z',
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending':
      return <AlertCircle className="h-4 w-4" />;
    case 'approved':
      return <CheckCircle className="h-4 w-4" />;
    case 'rejected':
      return <XCircle className="h-4 w-4" />;
    default:
      return <Clock className="h-4 w-4" />;
  }
};

export default function LeaveManagementPage() {
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>(mockLeaveRequests);
  const [filteredRequests, setFilteredRequests] = useState<LeaveRequest[]>(mockLeaveRequests);
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterUserType, setFilterUserType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');

  useEffect(() => {
    let filtered = leaveRequests;

    if (filterStatus !== 'all') {
      filtered = filtered.filter(request => request.status === filterStatus);
    }

    if (filterUserType !== 'all') {
      filtered = filtered.filter(request => request.user_type === filterUserType);
    }

    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.leave_type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredRequests(filtered);
  }, [leaveRequests, filterStatus, filterUserType, searchTerm]);

  const handleApprove = (requestId: string) => {
    setLeaveRequests(prev => prev.map(request =>
      request.id === requestId
        ? {
            ...request,
            status: 'approved' as const,
            approved_by: 'admin-1',
            approval_date: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
        : request
    ));
    toast.success('Leave request approved successfully');
    setSelectedRequest(null);
  };

  const handleReject = (requestId: string) => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    setLeaveRequests(prev => prev.map(request =>
      request.id === requestId
        ? {
            ...request,
            status: 'rejected' as const,
            approved_by: 'admin-1',
            rejection_reason: rejectionReason,
            updated_at: new Date().toISOString(),
          }
        : request
    ));
    toast.success('Leave request rejected');
    setSelectedRequest(null);
    setRejectionReason('');
  };

  const stats = {
    total: leaveRequests.length,
    pending: leaveRequests.filter(r => r.status === 'pending').length,
    approved: leaveRequests.filter(r => r.status === 'approved').length,
    rejected: leaveRequests.filter(r => r.status === 'rejected').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Leave Management</h1>
        <p className="text-gray-600">Manage and approve leave requests from teachers and students</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <Input
                id="search"
                placeholder="Search by reason or leave type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="userType">User Type</Label>
              <Select value={filterUserType} onValueChange={setFilterUserType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select user type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Users</SelectItem>
                  <SelectItem value="teacher">Teachers</SelectItem>
                  <SelectItem value="student">Students</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Leave Requests List */}
      <Card>
        <CardHeader>
          <CardTitle>Leave Requests</CardTitle>
          <CardDescription>
            {filteredRequests.length} request(s) found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredRequests.map((request) => (
              <div key={request.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-2">
                      <Badge className={getStatusColor(request.status)}>
                        {getStatusIcon(request.status)}
                        <span className="ml-1 capitalize">{request.status}</span>
                      </Badge>
                      <Badge variant="outline" className="capitalize">
                        {request.user_type}
                      </Badge>
                      <Badge variant="outline" className="capitalize">
                        {request.leave_type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <p className="font-medium text-gray-900 mb-1">{request.reason}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {request.start_date} to {request.end_date}
                      </span>
                      <span className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {request.user_id}
                      </span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedRequest(request)}
                        >
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>Leave Request Details</DialogTitle>
                          <DialogDescription>
                            Review and manage this leave request
                          </DialogDescription>
                        </DialogHeader>
                        {selectedRequest && (
                          <div className="space-y-4">
                            <div>
                              <Label>User</Label>
                              <p className="text-sm text-gray-600 capitalize">
                                {selectedRequest.user_type}: {selectedRequest.user_id}
                              </p>
                            </div>
                            <div>
                              <Label>Leave Type</Label>
                              <p className="text-sm text-gray-600 capitalize">
                                {selectedRequest.leave_type.replace('_', ' ')}
                              </p>
                            </div>
                            <div>
                              <Label>Duration</Label>
                              <p className="text-sm text-gray-600">
                                {selectedRequest.start_date} to {selectedRequest.end_date}
                              </p>
                            </div>
                            <div>
                              <Label>Reason</Label>
                              <p className="text-sm text-gray-600">{selectedRequest.reason}</p>
                            </div>
                            <div>
                              <Label>Status</Label>
                              <Badge className={getStatusColor(selectedRequest.status)}>
                                {getStatusIcon(selectedRequest.status)}
                                <span className="ml-1 capitalize">{selectedRequest.status}</span>
                              </Badge>
                            </div>
                            {selectedRequest.status === 'rejected' && selectedRequest.rejection_reason && (
                              <div>
                                <Label>Rejection Reason</Label>
                                <p className="text-sm text-gray-600">{selectedRequest.rejection_reason}</p>
                              </div>
                            )}
                            {selectedRequest.status === 'pending' && (
                              <div className="space-y-3">
                                <div>
                                  <Label htmlFor="rejectionReason">Rejection Reason (if rejecting)</Label>
                                  <Textarea
                                    id="rejectionReason"
                                    placeholder="Provide reason for rejection..."
                                    value={rejectionReason}
                                    onChange={(e) => setRejectionReason(e.target.value)}
                                  />
                                </div>
                                <div className="flex space-x-2">
                                  <Button
                                    onClick={() => handleApprove(selectedRequest.id)}
                                    className="flex-1"
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Approve
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    onClick={() => handleReject(selectedRequest.id)}
                                    className="flex-1"
                                  >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Reject
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </div>
            ))}
            {filteredRequests.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No leave requests found matching your criteria.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
