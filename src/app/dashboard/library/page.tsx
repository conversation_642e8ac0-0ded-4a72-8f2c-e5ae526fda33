'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Book, Plus, Search, Users, BookOpen, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { Book as BookType, BookTransaction, BookFormData } from '@/lib/types';

// Mock books data
const mockBooks: BookType[] = [
  {
    id: '1',
    title: 'To Kill a Mockingbird',
    author: '<PERSON>',
    isbn: '978-0-06-112008-4',
    category: 'Fiction',
    publisher: 'J.B. Li<PERSON>incott & Co.',
    publication_year: 1960,
    total_copies: 5,
    available_copies: 3,
    location: 'A-101',
    description: 'A classic American novel',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    title: 'The Great Gatsby',
    author: 'F. Scott Fitzgerald',
    isbn: '978-0-7432-7356-5',
    category: 'Fiction',
    publisher: 'Scribner',
    publication_year: 1925,
    total_copies: 4,
    available_copies: 2,
    location: 'A-102',
    description: 'American classic about the Jazz Age',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '3',
    title: 'Introduction to Algorithms',
    author: 'Thomas H. Cormen',
    isbn: '978-0-262-03384-8',
    category: 'Computer Science',
    publisher: 'MIT Press',
    publication_year: 2009,
    total_copies: 3,
    available_copies: 1,
    location: 'CS-201',
    description: 'Comprehensive guide to algorithms',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

// Mock transactions data
const mockTransactions: BookTransaction[] = [
  {
    id: '1',
    book_id: '1',
    user_id: 'student-1',
    user_type: 'student',
    transaction_type: 'borrow',
    transaction_date: '2024-01-10',
    due_date: '2024-01-24',
    status: 'active',
    processed_by: 'librarian-1',
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-10T10:00:00Z',
  },
  {
    id: '2',
    book_id: '2',
    user_id: 'teacher-1',
    user_type: 'teacher',
    transaction_type: 'borrow',
    transaction_date: '2024-01-08',
    due_date: '2024-01-22',
    status: 'active',
    processed_by: 'librarian-1',
    created_at: '2024-01-08T14:00:00Z',
    updated_at: '2024-01-08T14:00:00Z',
  },
];

export default function LibraryPage() {
  const [books, setBooks] = useState<BookType[]>(mockBooks);
  const [transactions, setTransactions] = useState<BookTransaction[]>(mockTransactions);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState<BookFormData>({
    title: '',
    author: '',
    isbn: '',
    category: '',
    publisher: '',
    publication_year: new Date().getFullYear(),
    total_copies: 1,
    location: '',
    description: '',
  });

  const resetForm = () => {
    setFormData({
      title: '',
      author: '',
      isbn: '',
      category: '',
      publisher: '',
      publication_year: new Date().getFullYear(),
      total_copies: 1,
      location: '',
      description: '',
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.author || !formData.isbn) {
      toast.error('Please fill in all required fields');
      return;
    }

    const newBook: BookType = {
      id: Date.now().toString(),
      ...formData,
      available_copies: formData.total_copies,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    setBooks(prev => [...prev, newBook]);
    toast.success('Book added successfully');
    setIsDialogOpen(false);
    resetForm();
  };

  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
    book.isbn.includes(searchTerm) ||
    book.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const activeTransactions = transactions.filter(t => t.status === 'active');
  const overdueTransactions = activeTransactions.filter(t => 
    t.due_date && new Date(t.due_date) < new Date()
  );

  const getBookById = (bookId: string) => books.find(b => b.id === bookId);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Library Management</h1>
          <p className="text-gray-600">Manage books and track borrowing</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add Book
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Book</DialogTitle>
              <DialogDescription>
                Add a new book to the library inventory
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    placeholder="Book title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="author">Author</Label>
                  <Input
                    id="author"
                    placeholder="Author name"
                    value={formData.author}
                    onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="isbn">ISBN</Label>
                  <Input
                    id="isbn"
                    placeholder="ISBN number"
                    value={formData.isbn}
                    onChange={(e) => setFormData(prev => ({ ...prev, isbn: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    placeholder="Book category"
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="publisher">Publisher</Label>
                  <Input
                    id="publisher"
                    placeholder="Publisher name"
                    value={formData.publisher}
                    onChange={(e) => setFormData(prev => ({ ...prev, publisher: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="publication_year">Publication Year</Label>
                  <Input
                    id="publication_year"
                    type="number"
                    min="1000"
                    max={new Date().getFullYear()}
                    value={formData.publication_year}
                    onChange={(e) => setFormData(prev => ({ ...prev, publication_year: parseInt(e.target.value) }))}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="total_copies">Total Copies</Label>
                  <Input
                    id="total_copies"
                    type="number"
                    min="1"
                    value={formData.total_copies}
                    onChange={(e) => setFormData(prev => ({ ...prev, total_copies: parseInt(e.target.value) }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    placeholder="Shelf/Section"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div className="flex space-x-2">
                <Button type="submit" className="flex-1">
                  Add Book
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Books</p>
                <p className="text-2xl font-bold text-gray-900">{books.length}</p>
              </div>
              <Book className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available</p>
                <p className="text-2xl font-bold text-green-600">
                  {books.reduce((sum, book) => sum + book.available_copies, 0)}
                </p>
              </div>
              <BookOpen className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Borrowed</p>
                <p className="text-2xl font-bold text-yellow-600">{activeTransactions.length}</p>
              </div>
              <Users className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{overdueTransactions.length}</p>
              </div>
              <Clock className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search books by title, author, ISBN, or category..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="books" className="space-y-4">
        <TabsList>
          <TabsTrigger value="books">Books</TabsTrigger>
          <TabsTrigger value="borrowed">Borrowed Books</TabsTrigger>
          <TabsTrigger value="overdue">Overdue</TabsTrigger>
        </TabsList>
        
        <TabsContent value="books">
          <Card>
            <CardHeader>
              <CardTitle>Book Inventory</CardTitle>
              <CardDescription>
                {filteredBooks.length} book(s) found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredBooks.map((book) => (
                  <div key={book.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="font-semibold text-gray-900">{book.title}</h3>
                          <Badge variant="outline">{book.category}</Badge>
                          <Badge className={book.available_copies > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {book.available_copies > 0 ? 'Available' : 'Out of Stock'}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-2">by {book.author}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <span>ISBN: {book.isbn}</span>
                          <span>Publisher: {book.publisher}</span>
                          <span>Year: {book.publication_year}</span>
                          <span>Location: {book.location}</span>
                        </div>
                        <div className="mt-2 text-sm text-gray-600">
                          <span>Available: {book.available_copies} / {book.total_copies}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {filteredBooks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No books found matching your search.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="borrowed">
          <Card>
            <CardHeader>
              <CardTitle>Currently Borrowed Books</CardTitle>
              <CardDescription>
                {activeTransactions.length} book(s) currently borrowed
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activeTransactions.map((transaction) => {
                  const book = getBookById(transaction.book_id);
                  const isOverdue = transaction.due_date && new Date(transaction.due_date) < new Date();
                  
                  return (
                    <div key={transaction.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-4 mb-2">
                            <h3 className="font-semibold text-gray-900">{book?.title}</h3>
                            <Badge variant="outline" className="capitalize">
                              {transaction.user_type}
                            </Badge>
                            {isOverdue && (
                              <Badge variant="destructive">Overdue</Badge>
                            )}
                          </div>
                          <p className="text-gray-600 mb-2">Borrowed by: {transaction.user_id}</p>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <span>Borrowed: {transaction.transaction_date}</span>
                            <span>Due: {transaction.due_date}</span>
                            <span>Processed by: {transaction.processed_by}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
                {activeTransactions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No books currently borrowed.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="overdue">
          <Card>
            <CardHeader>
              <CardTitle>Overdue Books</CardTitle>
              <CardDescription>
                {overdueTransactions.length} overdue book(s)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {overdueTransactions.map((transaction) => {
                  const book = getBookById(transaction.book_id);
                  const daysOverdue = transaction.due_date 
                    ? Math.floor((new Date().getTime() - new Date(transaction.due_date).getTime()) / (1000 * 60 * 60 * 24))
                    : 0;
                  
                  return (
                    <div key={transaction.id} className="border border-red-200 rounded-lg p-4 bg-red-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-4 mb-2">
                            <h3 className="font-semibold text-red-900">{book?.title}</h3>
                            <Badge variant="destructive">
                              {daysOverdue} day(s) overdue
                            </Badge>
                          </div>
                          <p className="text-red-800 mb-2">Borrowed by: {transaction.user_id}</p>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-red-700">
                            <span>Borrowed: {transaction.transaction_date}</span>
                            <span>Due: {transaction.due_date}</span>
                            <span>Type: {transaction.user_type}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
                {overdueTransactions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No overdue books.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
