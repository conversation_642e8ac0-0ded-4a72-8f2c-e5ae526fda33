@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  /* Modern gradient background */
  --background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --background-solid: #f8fafc;
  --foreground: #0f172a;

  /* Enhanced card styling */
  --card: #ffffff;
  --card-foreground: #0f172a;
  --card-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Modern color palette */
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --primary-solid: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #475569;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #e0e7ff;
  --accent-foreground: #3730a3;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  /* Enhanced borders and inputs */
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #3b82f6;

  /* Modern chart colors */
  --chart-1: #3b82f6;
  --chart-2: #10b981;
  --chart-3: #f59e0b;
  --chart-4: #ef4444;
  --chart-5: #8b5cf6;

  /* Enhanced sidebar */
  --sidebar: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  --sidebar-solid: #ffffff;
  --sidebar-foreground: #0f172a;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #475569;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #3b82f6;
}

.dark {
  --background: oklch(0.147 0.004 49.25);
  --foreground: oklch(0.985 0.001 106.423);
  --card: oklch(0.216 0.006 56.043);
  --card-foreground: oklch(0.985 0.001 106.423);
  --popover: oklch(0.216 0.006 56.043);
  --popover-foreground: oklch(0.985 0.001 106.423);
  --primary: oklch(0.923 0.003 48.717);
  --primary-foreground: oklch(0.216 0.006 56.043);
  --secondary: oklch(0.268 0.007 34.298);
  --secondary-foreground: oklch(0.985 0.001 106.423);
  --muted: oklch(0.268 0.007 34.298);
  --muted-foreground: oklch(0.709 0.01 56.259);
  --accent: oklch(0.268 0.007 34.298);
  --accent-foreground: oklch(0.985 0.001 106.423);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.553 0.013 58.071);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.216 0.006 56.043);
  --sidebar-foreground: oklch(0.985 0.001 106.423);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.268 0.007 34.298);
  --sidebar-accent-foreground: oklch(0.985 0.001 106.423);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.553 0.013 58.071);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply text-foreground antialiased;
    background: var(--background-solid);
    background-image: var(--background);
    min-height: 100vh;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced focus styles */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }
}

@layer components {
  /* Modern card styles */
  .card-modern {
    @apply bg-card text-card-foreground rounded-xl border border-border/50 backdrop-blur-sm;
    box-shadow: var(--card-shadow);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-modern:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Modern button styles */
  .btn-gradient {
    background: var(--primary);
    @apply text-primary-foreground border-0 shadow-lg;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-gradient:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
  }

  /* Animated background */
  .animated-bg {
    background: linear-gradient(-45deg, #3b82f6, #1d4ed8, #6366f1, #8b5cf6);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Modern sidebar styles */
  .sidebar-modern {
    background: var(--sidebar-solid);
    background-image: var(--sidebar);
    @apply border-r border-sidebar-border/50 backdrop-blur-sm;
  }

  /* Enhanced input styles */
  .input-modern {
    @apply bg-input border border-border rounded-lg px-4 py-2.5 text-sm;
    @apply focus:border-ring focus:ring-2 focus:ring-ring/20;
    @apply transition-all duration-200;
  }
}
