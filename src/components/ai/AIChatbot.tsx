'use client';

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Send, Bot, User, Loader2, X, Minimize2, Maximize2 } from 'lucide-react';
// import { toast } from 'sonner';

// Mock toast function for demo
const toast = {
  success: (message: string) => console.log('Success:', message),
  error: (message: string) => console.log('Error:', message),
  info: (message: string) => console.log('Info:', message),
};

interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
}

interface AIChatbotProps {
  userType?: 'student' | 'parent' | 'teacher' | 'admin';
  isMinimized?: boolean;
  onToggleMinimize?: () => void;
  onClose?: () => void;
}

export function AIChatbot({
  userType = 'student',
  isMinimized = false,
  onToggleMinimize,
  onClose
}: AIChatbotProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'bot',
      content: `Hello! I'm your AI assistant. I can help you with questions about school policies, schedules, fees, and more. How can I assist you today?`,
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // FAQ Knowledge Base
  const faqDatabase = {
    student: [
      {
        keywords: ['schedule', 'timetable', 'class', 'time'],
        response: 'You can view your class schedule in the Timetable section of your dashboard. Classes typically run from 8:00 AM to 3:00 PM, Monday through Friday.'
      },
      {
        keywords: ['fees', 'payment', 'tuition', 'cost'],
        response: 'School fees can be paid online through the Fees section in your dashboard. You can also view your payment history and download receipts there.'
      },
      {
        keywords: ['attendance', 'absent', 'leave'],
        response: 'You can view your attendance record in the Attendance section. If you need to apply for leave, please use the Leave Management feature or contact your class teacher.'
      },
      {
        keywords: ['grades', 'marks', 'results', 'exam'],
        response: 'Your exam results and grades are available in the Performance section of your dashboard. Results are typically published within 2 weeks of exam completion.'
      },
      {
        keywords: ['library', 'books', 'borrow'],
        response: 'The library is open from 8:00 AM to 5:00 PM. You can search for books and check your borrowed books in the Library section of your dashboard.'
      }
    ],
    parent: [
      {
        keywords: ['fees', 'payment', 'tuition', 'bill'],
        response: 'You can view and pay your child\'s school fees through the parent portal. Payment can be made online using credit/debit cards or bank transfer.'
      },
      {
        keywords: ['attendance', 'absent', 'sick'],
        response: 'You can view your child\'s attendance record in the parent dashboard. To report absence due to illness, please contact the school office or use the leave application feature.'
      },
      {
        keywords: ['teacher', 'contact', 'meeting', 'appointment'],
        response: 'You can contact your child\'s teachers through the messaging system in the parent portal. Parent-teacher meetings are scheduled quarterly.'
      },
      {
        keywords: ['transport', 'bus', 'pickup', 'drop'],
        response: 'School transport information including routes, timings, and fees are available in the Transport section. Contact the transport coordinator for any changes.'
      }
    ],
    teacher: [
      {
        keywords: ['attendance', 'mark', 'student'],
        response: 'You can mark student attendance through the Attendance section in your teacher dashboard. Attendance should be marked within the first 15 minutes of each class.'
      },
      {
        keywords: ['grades', 'marks', 'assessment'],
        response: 'Student grades can be entered through the Grades section. Make sure to submit grades before the deadline specified by the administration.'
      },
      {
        keywords: ['leave', 'substitute', 'absent'],
        response: 'To apply for leave, use the Leave Management system. Please ensure you arrange for a substitute teacher and inform the administration in advance.'
      }
    ],
    admin: [
      {
        keywords: ['reports', 'analytics', 'data'],
        response: 'Comprehensive reports and analytics are available in the Analytics dashboard. You can generate custom reports for attendance, fees, performance, and more.'
      },
      {
        keywords: ['backup', 'data', 'export'],
        response: 'Data backup and export features are available in the Settings section. Regular automated backups are performed daily at 2:00 AM.'
      }
    ]
  };

  const quickReplies = {
    student: ['View Schedule', 'Check Fees', 'Attendance Record', 'Library Books'],
    parent: ['Pay Fees', 'Child\'s Attendance', 'Contact Teacher', 'Transport Info'],
    teacher: ['Mark Attendance', 'Enter Grades', 'Apply Leave', 'Class Schedule'],
    admin: ['Generate Reports', 'System Settings', 'User Management', 'Data Export']
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateBotResponse = async (userMessage: string): Promise<string> => {
    const userFAQs = faqDatabase[userType] || faqDatabase.student;
    const lowerMessage = userMessage.toLowerCase();

    // Find matching FAQ
    const matchingFAQ = userFAQs.find(faq =>
      faq.keywords.some(keyword => lowerMessage.includes(keyword))
    );

    if (matchingFAQ) {
      return matchingFAQ.response;
    }

    // Default responses for common greetings
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      return `Hello! I'm here to help you with any questions about school. You can ask me about schedules, fees, attendance, or any other school-related topics.`;
    }

    if (lowerMessage.includes('thank')) {
      return `You're welcome! Is there anything else I can help you with?`;
    }

    // Default fallback response
    return `I understand you're asking about "${userMessage}". While I don't have specific information about that topic, you can:

• Contact the school office at (555) 123-4567
• Email <NAME_EMAIL>
• Visit the main office during school hours
• Check the relevant section in your dashboard

Is there anything else I can help you with?`;
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // Add typing indicator
    const typingMessage: ChatMessage = {
      id: 'typing',
      type: 'bot',
      content: '',
      timestamp: new Date(),
      isTyping: true
    };
    setMessages(prev => [...prev, typingMessage]);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      const botResponse = await generateBotResponse(userMessage.content);

      // Remove typing indicator and add actual response
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== 'typing');
        return [...filtered, {
          id: (Date.now() + 1).toString(),
          type: 'bot',
          content: botResponse,
          timestamp: new Date()
        }];
      });
    } catch (error) {
      console.error('Error generating response:', error);
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== 'typing');
        return [...filtered, {
          id: (Date.now() + 1).toString(),
          type: 'bot',
          content: 'I apologize, but I\'m having trouble processing your request right now. Please try again or contact the school office directly.',
          timestamp: new Date()
        }];
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickReply = (reply: string) => {
    setInputMessage(reply);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={onToggleMinimize}
          className="btn-gradient rounded-full w-14 h-14 shadow-lg hover:shadow-xl"
        >
          <MessageSquare className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)]">
      <Card className="card-modern border-0 shadow-2xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div>
                <CardTitle className="text-sm">AI Assistant</CardTitle>
                <Badge variant="secondary" className="text-xs">
                  Online
                </Badge>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleMinimize}
                className="h-8 w-8 p-0"
              >
                <Minimize2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {/* Messages */}
          <div className="h-80 overflow-y-auto p-4 space-y-3">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-xl p-3 ${
                    message.type === 'user'
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  {message.isTyping ? (
                    <div className="flex items-center space-x-1">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-xs text-gray-500 ml-2">AI is typing...</span>
                    </div>
                  ) : (
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Replies */}
          <div className="px-4 pb-3">
            <div className="flex flex-wrap gap-2">
              {quickReplies[userType]?.map((reply) => (
                <Button
                  key={reply}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickReply(reply)}
                  className="text-xs"
                >
                  {reply}
                </Button>
              ))}
            </div>
          </div>

          {/* Input */}
          <div className="p-4 border-t border-border/50">
            <div className="flex space-x-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="input-modern"
                disabled={isLoading}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isLoading}
                className="btn-gradient"
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
