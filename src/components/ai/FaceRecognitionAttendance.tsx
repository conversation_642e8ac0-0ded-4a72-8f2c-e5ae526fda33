'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Camera, CameraOff, Users, CheckCircle, XCircle, Loader2 } from 'lucide-react';
// import { toast } from 'sonner';

// Mock toast function for demo
const toast = {
  success: (message: string) => console.log('Success:', message),
  error: (message: string) => console.log('Error:', message),
  info: (message: string) => console.log('Info:', message),
};

interface DetectedStudent {
  id: string;
  name: string;
  confidence: number;
  timestamp: Date;
  status: 'present' | 'absent';
}

interface FaceRecognitionAttendanceProps {
  classId?: string;
  onAttendanceMarked?: (students: DetectedStudent[]) => void;
}

export function FaceRecognitionAttendance({
  classId,
  onAttendanceMarked
}: FaceRecognitionAttendanceProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [detectedStudents, setDetectedStudents] = useState<DetectedStudent[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);

  // Mock student database for demo
  const mockStudentDatabase = [
    { id: 'STU001', name: 'John Doe', faceDescriptor: 'mock_descriptor_1' },
    { id: 'STU002', name: 'Jane Smith', faceDescriptor: 'mock_descriptor_2' },
    { id: 'STU003', name: 'Mike Johnson', faceDescriptor: 'mock_descriptor_3' },
    { id: 'STU004', name: 'Sarah Wilson', faceDescriptor: 'mock_descriptor_4' },
    { id: 'STU005', name: 'David Brown', faceDescriptor: 'mock_descriptor_5' },
  ];

  const startCamera = async () => {
    try {
      setIsLoading(true);
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: 640,
          height: 480,
          facingMode: 'user'
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);
        setIsCameraActive(true);
        toast.success('Camera started successfully');
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Failed to access camera. Please check permissions.');
    } finally {
      setIsLoading(false);
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsCameraActive(false);
    toast.info('Camera stopped');
  };

  const captureAndProcessFrame = async () => {
    if (!videoRef.current || !canvasRef.current || isProcessing) return;

    setIsProcessing(true);
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    try {
      // Simulate face detection and recognition
      await simulateFaceRecognition();
    } catch (error) {
      console.error('Face recognition error:', error);
      toast.error('Face recognition failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const simulateFaceRecognition = async (): Promise<void> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate detecting random students for demo
        const numDetected = Math.floor(Math.random() * 3) + 1;
        const detected: DetectedStudent[] = [];

        for (let i = 0; i < numDetected; i++) {
          const randomStudent = mockStudentDatabase[Math.floor(Math.random() * mockStudentDatabase.length)];
          const confidence = 0.7 + Math.random() * 0.3; // 70-100% confidence

          if (!detected.find(s => s.id === randomStudent.id)) {
            detected.push({
              id: randomStudent.id,
              name: randomStudent.name,
              confidence,
              timestamp: new Date(),
              status: 'present'
            });
          }
        }

        setDetectedStudents(prev => {
          const updated = [...prev];
          detected.forEach(newStudent => {
            const existingIndex = updated.findIndex(s => s.id === newStudent.id);
            if (existingIndex >= 0) {
              updated[existingIndex] = newStudent;
            } else {
              updated.push(newStudent);
            }
          });
          return updated;
        });

        if (detected.length > 0) {
          toast.success(`Detected ${detected.length} student(s)`);
        }

        resolve();
      }, 2000);
    });
  };

  const markAttendance = () => {
    if (detectedStudents.length === 0) {
      toast.error('No students detected. Please capture faces first.');
      return;
    }

    // Mark attendance for all detected students
    const attendanceData = detectedStudents.map(student => ({
      ...student,
      status: 'present' as const
    }));

    onAttendanceMarked?.(attendanceData);
    toast.success(`Attendance marked for ${attendanceData.length} students`);

    // Clear detected students after marking attendance
    setDetectedStudents([]);
  };

  const removeDetectedStudent = (studentId: string) => {
    setDetectedStudents(prev => prev.filter(s => s.id !== studentId));
  };

  useEffect(() => {
    return () => {
      // Cleanup camera stream on unmount
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  return (
    <div className="space-y-6">
      {/* Camera Control */}
      <Card className="card-modern border-0">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5 text-blue-600" />
                <span>AI Face Recognition Attendance</span>
              </CardTitle>
              <CardDescription>
                Use AI-powered face recognition to automatically mark student attendance
              </CardDescription>
            </div>
            <Badge variant={isCameraActive ? "default" : "secondary"}>
              {isCameraActive ? "Camera Active" : "Camera Inactive"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Video Feed */}
          <div className="relative bg-gray-100 rounded-xl overflow-hidden">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-64 object-cover"
              style={{ display: isCameraActive ? 'block' : 'none' }}
            />
            <canvas
              ref={canvasRef}
              className="hidden"
            />
            {!isCameraActive && (
              <div className="w-full h-64 flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                <div className="text-center">
                  <CameraOff className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Camera not active</p>
                </div>
              </div>
            )}
            {isProcessing && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                <div className="bg-white rounded-lg p-4 flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Processing faces...</span>
                </div>
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="flex space-x-3">
            {!isCameraActive ? (
              <Button
                onClick={startCamera}
                disabled={isLoading}
                className="btn-gradient"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Starting...
                  </>
                ) : (
                  <>
                    <Camera className="h-4 w-4 mr-2" />
                    Start Camera
                  </>
                )}
              </Button>
            ) : (
              <>
                <Button onClick={stopCamera} variant="outline">
                  <CameraOff className="h-4 w-4 mr-2" />
                  Stop Camera
                </Button>
                <Button
                  onClick={captureAndProcessFrame}
                  disabled={isProcessing}
                  className="btn-gradient"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Users className="h-4 w-4 mr-2" />
                      Detect Faces
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detected Students */}
      {detectedStudents.length > 0 && (
        <Card className="card-modern border-0">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Detected Students ({detectedStudents.length})</span>
            </CardTitle>
            <CardDescription>
              Review detected students before marking attendance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {detectedStudents.map((student) => (
                <div
                  key={student.id}
                  className="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/20"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {student.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div>
                      <p className="font-medium">{student.name}</p>
                      <p className="text-sm text-muted-foreground">
                        ID: {student.id} • Confidence: {(student.confidence * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="default" className="bg-green-100 text-green-700">
                      Present
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeDetectedStudent(student.id)}
                      className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                    >
                      <XCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex justify-end">
              <Button onClick={markAttendance} className="btn-gradient">
                <CheckCircle className="h-4 w-4 mr-2" />
                Mark Attendance for {detectedStudents.length} Students
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
