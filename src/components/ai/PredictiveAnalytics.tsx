'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  TrendingDown,
  TrendingUp,
  AlertTriangle,
  Users,
  DollarSign,
  GraduationCap,
  Brain,
  Target,
  Zap
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface PredictionResult {
  studentId: string;
  studentName: string;
  riskLevel: 'low' | 'medium' | 'high';
  riskScore: number;
  factors: string[];
  recommendation: string;
}

interface AnalyticsData {
  dropoutPredictions: PredictionResult[];
  feeDefaultPredictions: PredictionResult[];
  performanceTrends: any[];
  attendancePatterns: any[];
}

export function PredictiveAnalytics() {
  const [selectedModel, setSelectedModel] = useState<'dropout' | 'fee_default' | 'performance'>('dropout');
  const [isLoading, setIsLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  // Mock predictive analytics data
  const mockAnalyticsData: AnalyticsData = {
    dropoutPredictions: [
      {
        studentId: 'STU001',
        studentName: 'John Doe',
        riskLevel: 'high',
        riskScore: 0.85,
        factors: ['Low attendance (65%)', 'Declining grades', 'Financial difficulties'],
        recommendation: 'Immediate intervention required. Schedule counseling session and financial aid review.'
      },
      {
        studentId: 'STU002',
        studentName: 'Jane Smith',
        riskLevel: 'medium',
        riskScore: 0.62,
        factors: ['Irregular attendance', 'Family issues'],
        recommendation: 'Monitor closely. Provide additional academic support and family counseling.'
      },
      {
        studentId: 'STU003',
        studentName: 'Mike Johnson',
        riskLevel: 'low',
        riskScore: 0.23,
        factors: ['Good academic performance', 'Regular attendance'],
        recommendation: 'Continue current support. Student is performing well.'
      }
    ],
    feeDefaultPredictions: [
      {
        studentId: 'STU004',
        studentName: 'Sarah Wilson',
        riskLevel: 'high',
        riskScore: 0.78,
        factors: ['Previous late payments', 'Economic background', 'Parent job loss'],
        recommendation: 'Offer payment plan options and financial assistance programs.'
      },
      {
        studentId: 'STU005',
        studentName: 'David Brown',
        riskLevel: 'medium',
        riskScore: 0.55,
        factors: ['Seasonal payment delays', 'Multiple siblings'],
        recommendation: 'Provide flexible payment schedule and sibling discount information.'
      }
    ],
    performanceTrends: [
      { month: 'Jan', predicted: 85, actual: 82 },
      { month: 'Feb', predicted: 87, actual: 85 },
      { month: 'Mar', predicted: 84, actual: 86 },
      { month: 'Apr', predicted: 88, actual: 87 },
      { month: 'May', predicted: 86, actual: null },
      { month: 'Jun', predicted: 89, actual: null }
    ],
    attendancePatterns: [
      { day: 'Monday', attendance: 92 },
      { day: 'Tuesday', attendance: 95 },
      { day: 'Wednesday', attendance: 94 },
      { day: 'Thursday', attendance: 93 },
      { day: 'Friday', attendance: 88 }
    ]
  };

  const runPredictiveAnalysis = async () => {
    setIsLoading(true);

    // Simulate AI model processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    setAnalyticsData(mockAnalyticsData);
    setIsLoading(false);
  };

  useEffect(() => {
    runPredictiveAnalysis();
  }, []);

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high': return <AlertTriangle className="h-4 w-4" />;
      case 'medium': return <TrendingDown className="h-4 w-4" />;
      case 'low': return <TrendingUp className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const currentPredictions = selectedModel === 'dropout'
    ? analyticsData?.dropoutPredictions
    : analyticsData?.feeDefaultPredictions;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold gradient-text flex items-center space-x-3">
            <Brain className="h-8 w-8 text-blue-600" />
            <span>AI Predictive Analytics</span>
          </h1>
          <p className="text-muted-foreground text-lg">
            Advanced AI models to predict student outcomes and optimize interventions
          </p>
        </div>
        <Button
          onClick={runPredictiveAnalysis}
          disabled={isLoading}
          className="btn-gradient"
        >
          {isLoading ? (
            <>
              <Zap className="h-4 w-4 mr-2 animate-pulse" />
              Analyzing...
            </>
          ) : (
            <>
              <Zap className="h-4 w-4 mr-2" />
              Run Analysis
            </>
          )}
        </Button>
      </div>

      {/* Model Selection */}
      <Card className="card-modern border-0">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-purple-600" />
            <span>Prediction Models</span>
          </CardTitle>
          <CardDescription>
            Select the AI model to view predictions and insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Button
              variant={selectedModel === 'dropout' ? 'default' : 'outline'}
              onClick={() => setSelectedModel('dropout')}
              className="btn-gradient"
            >
              Student Dropout Risk
            </Button>
            <Button
              variant={selectedModel === 'fee_default' ? 'default' : 'outline'}
              onClick={() => setSelectedModel('fee_default')}
            >
              Fee Payment Default
            </Button>
            <Button
              variant={selectedModel === 'performance' ? 'default' : 'outline'}
              onClick={() => setSelectedModel('performance')}
            >
              Academic Performance
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Predictions Results */}
      {currentPredictions && (
        <Card className="card-modern border-0">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {selectedModel === 'dropout' ? (
                <>
                  <Users className="h-5 w-5 text-red-600" />
                  <span>Student Dropout Risk Predictions</span>
                </>
              ) : (
                <>
                  <DollarSign className="h-5 w-5 text-orange-600" />
                  <span>Fee Payment Default Predictions</span>
                </>
              )}
            </CardTitle>
            <CardDescription>
              AI-powered predictions with risk scores and recommended interventions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentPredictions.map((prediction) => (
                <div
                  key={prediction.studentId}
                  className="p-6 bg-white/50 rounded-xl border border-white/20 hover:bg-white/70 transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {prediction.studentName.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{prediction.studentName}</h3>
                        <p className="text-sm text-muted-foreground">ID: {prediction.studentId}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge className={`${getRiskColor(prediction.riskLevel)} border`}>
                        {getRiskIcon(prediction.riskLevel)}
                        <span className="ml-1 capitalize">{prediction.riskLevel} Risk</span>
                      </Badge>
                      <p className="text-sm text-muted-foreground mt-1">
                        Score: {(prediction.riskScore * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">Risk Factors</h4>
                      <ul className="space-y-1">
                        {prediction.factors.map((factor, index) => (
                          <li key={index} className="text-sm flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 bg-red-400 rounded-full"></div>
                            <span>{factor}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">Recommendation</h4>
                      <p className="text-sm bg-blue-50 p-3 rounded-lg border border-blue-200">
                        {prediction.recommendation}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Performance Trends */}
        <Card className="card-modern border-0 bg-gradient-to-br from-white to-purple-50/30">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <GraduationCap className="h-5 w-5 text-purple-600" />
              <span>Performance Prediction vs Actual</span>
            </CardTitle>
            <CardDescription>
              AI model predictions compared to actual performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData?.performanceTrends}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis dataKey="month" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="predicted"
                  stroke="#8b5cf6"
                  strokeWidth={3}
                  strokeDasharray="5 5"
                  name="AI Prediction"
                />
                <Line
                  type="monotone"
                  dataKey="actual"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  name="Actual Performance"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Attendance Patterns */}
        <Card className="card-modern border-0 bg-gradient-to-br from-white to-green-50/30">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-600" />
              <span>Weekly Attendance Patterns</span>
            </CardTitle>
            <CardDescription>
              AI-identified patterns in student attendance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analyticsData?.attendancePatterns}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis dataKey="day" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Bar
                  dataKey="attendance"
                  fill="#10b981"
                  radius={[4, 4, 0, 0]}
                  name="Attendance %"
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card className="card-modern border-0 bg-gradient-to-br from-white to-blue-50/30">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-blue-600" />
            <span>AI-Generated Insights</span>
          </CardTitle>
          <CardDescription>
            Key insights and recommendations from AI analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 bg-white/50 rounded-xl border border-white/20">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <h3 className="font-semibold text-red-700">High Risk Students</h3>
              </div>
              <p className="text-2xl font-bold text-red-600">3</p>
              <p className="text-sm text-muted-foreground">Require immediate intervention</p>
            </div>

            <div className="p-4 bg-white/50 rounded-xl border border-white/20">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-5 w-5 text-green-500" />
                <h3 className="font-semibold text-green-700">Improvement Rate</h3>
              </div>
              <p className="text-2xl font-bold text-green-600">87%</p>
              <p className="text-sm text-muted-foreground">Students showing improvement</p>
            </div>

            <div className="p-4 bg-white/50 rounded-xl border border-white/20">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="h-5 w-5 text-blue-500" />
                <h3 className="font-semibold text-blue-700">Model Accuracy</h3>
              </div>
              <p className="text-2xl font-bold text-blue-600">94.2%</p>
              <p className="text-sm text-muted-foreground">Prediction accuracy rate</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
