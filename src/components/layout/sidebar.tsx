'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  GraduationCap,
  BookOpen,
  Calendar,
  DollarSign,
  Bell,
  Settings,
  LogOut,
  School,
  FileText,
  ClipboardList,
  CalendarDays,
  Megaphone,
  Book,
  BarChart3,
  Brain,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { auth } from '@/lib/supabase';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Students', href: '/dashboard/students', icon: Users },
  { name: 'Teachers', href: '/dashboard/teachers', icon: GraduationCap },
  { name: 'Classes', href: '/dashboard/classes', icon: BookOpen },
  { name: 'Attendance', href: '/dashboard/attendance', icon: Calendar },
  { name: 'Timetable', href: '/dashboard/timetable', icon: Calendar },
  { name: 'Leave Management', href: '/dashboard/leave-management', icon: FileText },
  { name: '<PERSON>ams', href: '/dashboard/exams', icon: ClipboardList },
  { name: 'Events', href: '/dashboard/events', icon: CalendarDays },
  { name: 'Announcements', href: '/dashboard/announcements', icon: Megaphone },
  { name: 'Library', href: '/dashboard/library', icon: Book },
  { name: 'AI Features', href: '/dashboard/ai-features', icon: Brain },
  { name: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 },
  { name: 'Fees', href: '/dashboard/fees', icon: DollarSign },
  { name: 'Notifications', href: '/dashboard/notifications', icon: Bell },
  { name: 'Settings', href: '/dashboard/settings', icon: Settings },
];

export function Sidebar() {
  const pathname = usePathname();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      // Demo mode - skip actual logout
      if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
        toast.success('Logged out successfully');
        router.push('/login');
        return;
      }

      // Real Supabase logout
      await auth.signOut();
      toast.success('Logged out successfully');
      router.push('/login');
    } catch {
      toast.error('Error logging out');
    }
  };

  return (
    <div className="flex h-full w-64 flex-col sidebar-modern shadow-xl">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-sidebar-border/30 backdrop-blur-sm">
        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
          <School className="h-6 w-6 text-white" />
        </div>
        <span className="ml-3 text-xl font-bold gradient-text">
          School Admin
        </span>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-2 px-3 py-6">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 relative overflow-hidden',
                isActive
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/25 transform scale-105'
                  : 'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground hover:transform hover:scale-105'
              )}
            >
              <div className={cn(
                'flex items-center justify-center w-8 h-8 rounded-lg mr-3 transition-all duration-200',
                isActive
                  ? 'bg-white/20 text-white'
                  : 'bg-sidebar-accent/30 text-sidebar-foreground group-hover:bg-sidebar-accent group-hover:text-sidebar-accent-foreground'
              )}>
                <item.icon className="h-4 w-4" />
              </div>
              <span className="font-medium">{item.name}</span>
              {isActive && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-600/20 rounded-xl animate-pulse" />
              )}
            </Link>
          );
        })}
      </nav>

      {/* Logout Button */}
      <div className="p-3 border-t border-sidebar-border/30">
        <Button
          variant="ghost"
          className="w-full justify-start text-sidebar-foreground hover:bg-red-50 hover:text-red-600 rounded-xl py-3 px-4 transition-all duration-200 group"
          onClick={handleLogout}
        >
          <div className="flex items-center justify-center w-8 h-8 rounded-lg mr-3 bg-red-50 text-red-500 group-hover:bg-red-100 transition-all duration-200">
            <LogOut className="h-4 w-4" />
          </div>
          <span className="font-medium">Logout</span>
        </Button>
      </div>
    </div>
  );
}
