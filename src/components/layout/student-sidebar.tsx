'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Calendar,
  Clock,
  Bell,
  Settings,
  LogOut,
  User,
  BookOpen,
  FileText,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

const navigation = [
  { name: 'Dashboard', href: '/student', icon: LayoutDashboard },
  { name: 'My Attendance', href: '/student/attendance', icon: Calendar },
  { name: 'My Timetable', href: '/student/timetable', icon: Clock },
  { name: 'Leave Requests', href: '/student/leave', icon: FileText },
  { name: 'Notifications', href: '/student/notifications', icon: Bell },
  { name: 'Settings', href: '/student/settings', icon: Settings },
];

export function StudentSidebar() {
  const pathname = usePathname();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      // Demo mode - clear localStorage
      if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
        localStorage.removeItem('userRole');
        localStorage.removeItem('userId');
        localStorage.removeItem('studentName');
        localStorage.removeItem('studentId');
        localStorage.removeItem('studentClass');
        toast.success('Logged out successfully');
        router.push('/login');
        return;
      }

      // Real logout would go here
      toast.success('Logged out successfully');
      router.push('/login');
    } catch (error) {
      toast.error('Error logging out');
    }
  };

  // Get student info from localStorage for demo
  const studentName = typeof window !== 'undefined' ? localStorage.getItem('studentName') || 'Student' : 'Student';
  const studentId = typeof window !== 'undefined' ? localStorage.getItem('studentId') || 'STU001' : 'STU001';
  const studentClass = typeof window !== 'undefined' ? localStorage.getItem('studentClass') || 'Grade 5-A' : 'Grade 5-A';

  return (
    <div className="flex h-full w-64 flex-col bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-gray-200">
        <BookOpen className="h-8 w-8 text-purple-600" />
        <span className="ml-2 text-xl font-semibold text-gray-900">
          Student Portal
        </span>
      </div>

      {/* Student Info */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <User className="h-5 w-5 text-purple-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">{studentName}</p>
            <p className="text-xs text-gray-500">{studentId} • {studentClass}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon
                className={cn(
                  'mr-3 h-5 w-5 flex-shrink-0',
                  isActive ? 'text-purple-700' : 'text-gray-400 group-hover:text-gray-500'
                )}
              />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* Logout Button */}
      <div className="p-3 border-t border-gray-200">
        <Button
          variant="ghost"
          className="w-full justify-start text-gray-700 hover:bg-gray-50"
          onClick={handleLogout}
        >
          <LogOut className="mr-3 h-5 w-5" />
          Logout
        </Button>
      </div>
    </div>
  );
}
