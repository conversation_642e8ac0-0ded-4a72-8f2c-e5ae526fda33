'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  Calendar,
  Clock,
  MessageSquare,
  Settings,
  LogOut,
  GraduationCap,
  FileText,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

const navigation = [
  { name: 'Dashboard', href: '/teacher', icon: LayoutDashboard },
  { name: 'My Students', href: '/teacher/students', icon: Users },
  { name: 'Attendance', href: '/teacher/attendance', icon: Calendar },
  { name: 'My Timetable', href: '/teacher/timetable', icon: Clock },
  { name: 'Leave Requests', href: '/teacher/leave', icon: FileText },
  { name: 'Performance Remarks', href: '/teacher/remarks', icon: MessageSquare },
  { name: 'Settings', href: '/teacher/settings', icon: Settings },
];

export function TeacherSidebar() {
  const pathname = usePathname();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      // Demo mode - clear localStorage
      if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
        localStorage.removeItem('userRole');
        localStorage.removeItem('userId');
        localStorage.removeItem('teacherName');
        toast.success('Logged out successfully');
        router.push('/login');
        return;
      }

      // Real logout would go here
      toast.success('Logged out successfully');
      router.push('/login');
    } catch (error) {
      toast.error('Error logging out');
    }
  };

  // Get teacher name from localStorage for demo
  const teacherName = typeof window !== 'undefined' ? localStorage.getItem('teacherName') || 'Teacher' : 'Teacher';

  return (
    <div className="flex h-full w-64 flex-col bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-gray-200">
        <GraduationCap className="h-8 w-8 text-green-600" />
        <span className="ml-2 text-xl font-semibold text-gray-900">
          Teacher Portal
        </span>
      </div>

      {/* Teacher Info */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
            <GraduationCap className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">{teacherName}</p>
            <p className="text-xs text-gray-500">Mathematics Teacher</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-green-50 text-green-700 border-r-2 border-green-700'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon
                className={cn(
                  'mr-3 h-5 w-5 flex-shrink-0',
                  isActive ? 'text-green-700' : 'text-gray-400 group-hover:text-gray-500'
                )}
              />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* Logout Button */}
      <div className="p-3 border-t border-gray-200">
        <Button
          variant="ghost"
          className="w-full justify-start text-gray-700 hover:bg-gray-50"
          onClick={handleLogout}
        >
          <LogOut className="mr-3 h-5 w-5" />
          Logout
        </Button>
      </div>
    </div>
  );
}
