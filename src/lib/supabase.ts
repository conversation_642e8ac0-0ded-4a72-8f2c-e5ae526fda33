import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database helper functions
export const db = {
  // Students
  students: {
    getAll: () => supabase.from('students').select('*'),
    getById: (id: string) => supabase.from('students').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('students').insert(data),
    update: (id: string, data: any) => supabase.from('students').update(data).eq('id', id),
    delete: (id: string) => supabase.from('students').delete().eq('id', id),
  },

  // Teachers
  teachers: {
    getAll: () => supabase.from('teachers').select('*'),
    getById: (id: string) => supabase.from('teachers').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('teachers').insert(data),
    update: (id: string, data: any) => supabase.from('teachers').update(data).eq('id', id),
    delete: (id: string) => supabase.from('teachers').delete().eq('id', id),
  },

  // Classes
  classes: {
    getAll: () => supabase.from('classes').select('*'),
    getById: (id: string) => supabase.from('classes').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('classes').insert(data),
    update: (id: string, data: any) => supabase.from('classes').update(data).eq('id', id),
    delete: (id: string) => supabase.from('classes').delete().eq('id', id),
  },

  // Attendance
  attendance: {
    getAll: () => supabase.from('attendance').select('*'),
    getByDate: (date: string) => supabase.from('attendance').select('*').eq('date', date),
    getByStudent: (studentId: string) => supabase.from('attendance').select('*').eq('student_id', studentId),
    create: (data: any) => supabase.from('attendance').insert(data),
    update: (id: string, data: any) => supabase.from('attendance').update(data).eq('id', id),
  },

  // Timetable
  timetable: {
    getAll: () => supabase.from('timetable').select('*'),
    getByClass: (classId: string) => supabase.from('timetable').select('*').eq('class_id', classId),
    create: (data: any) => supabase.from('timetable').insert(data),
    update: (id: string, data: any) => supabase.from('timetable').update(data).eq('id', id),
    delete: (id: string) => supabase.from('timetable').delete().eq('id', id),
  },

  // Fees
  fees: {
    getAll: () => supabase.from('fees').select('*'),
    getByStudent: (studentId: string) => supabase.from('fees').select('*').eq('student_id', studentId),
    getPending: () => supabase.from('fees').select('*').eq('status', 'pending'),
    create: (data: any) => supabase.from('fees').insert(data),
    update: (id: string, data: any) => supabase.from('fees').update(data).eq('id', id),
  },

  // Notifications
  notifications: {
    getAll: () => supabase.from('notifications').select('*'),
    getRecent: (limit: number = 10) => supabase.from('notifications').select('*').order('created_at', { ascending: false }).limit(limit),
    create: (data: any) => supabase.from('notifications').insert(data),
    markAsRead: (id: string, userId: string) => {
      // This would need custom logic to update the read_by array
      return supabase.rpc('mark_notification_read', { notification_id: id, user_id: userId });
    },
  },

  // Leave Management
  leaveRequests: {
    getAll: () => supabase.from('leave_requests').select('*'),
    getById: (id: string) => supabase.from('leave_requests').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('leave_requests').insert(data),
    update: (id: string, data: any) => supabase.from('leave_requests').update(data).eq('id', id),
    delete: (id: string) => supabase.from('leave_requests').delete().eq('id', id),
    getByUser: (userId: string) => supabase.from('leave_requests').select('*').eq('user_id', userId),
    getByStatus: (status: string) => supabase.from('leave_requests').select('*').eq('status', status),
    getPending: () => supabase.from('leave_requests').select('*').eq('status', 'pending'),
  },

  // Exam Management
  exams: {
    getAll: () => supabase.from('exams').select('*'),
    getById: (id: string) => supabase.from('exams').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('exams').insert(data),
    update: (id: string, data: any) => supabase.from('exams').update(data).eq('id', id),
    delete: (id: string) => supabase.from('exams').delete().eq('id', id),
    getByClass: (classId: string) => supabase.from('exams').select('*').eq('class_id', classId),
    getBySubject: (subjectId: string) => supabase.from('exams').select('*').eq('subject_id', subjectId),
    getUpcoming: () => supabase.from('exams').select('*').gte('exam_date', new Date().toISOString().split('T')[0]),
  },

  // Grade Management
  grades: {
    getAll: () => supabase.from('grades').select('*'),
    getById: (id: string) => supabase.from('grades').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('grades').insert(data),
    update: (id: string, data: any) => supabase.from('grades').update(data).eq('id', id),
    delete: (id: string) => supabase.from('grades').delete().eq('id', id),
    getByStudent: (studentId: string) => supabase.from('grades').select('*').eq('student_id', studentId),
    getByExam: (examId: string) => supabase.from('grades').select('*').eq('exam_id', examId),
    getStudentGrades: (studentId: string) => supabase.from('grades').select('*, exams(*)').eq('student_id', studentId),
  },

  // Event Management
  events: {
    getAll: () => supabase.from('events').select('*'),
    getById: (id: string) => supabase.from('events').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('events').insert(data),
    update: (id: string, data: any) => supabase.from('events').update(data).eq('id', id),
    delete: (id: string) => supabase.from('events').delete().eq('id', id),
    getUpcoming: () => supabase.from('events').select('*').gte('start_date', new Date().toISOString().split('T')[0]),
    getByDateRange: (startDate: string, endDate: string) => supabase.from('events').select('*').gte('start_date', startDate).lte('end_date', endDate),
    getHolidays: () => supabase.from('events').select('*').eq('is_holiday', true),
  },

  // Announcement Management
  announcements: {
    getAll: () => supabase.from('announcements').select('*'),
    getById: (id: string) => supabase.from('announcements').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('announcements').insert(data),
    update: (id: string, data: any) => supabase.from('announcements').update(data).eq('id', id),
    delete: (id: string) => supabase.from('announcements').delete().eq('id', id),
    getActive: () => supabase.from('announcements').select('*').or('expires_at.is.null,expires_at.gte.' + new Date().toISOString()),
    getEmergency: () => supabase.from('announcements').select('*').eq('is_emergency', true),
    getByAudience: (audience: string) => supabase.from('announcements').select('*').eq('target_audience', audience),
  },

  // Library Management
  books: {
    getAll: () => supabase.from('books').select('*'),
    getById: (id: string) => supabase.from('books').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('books').insert(data),
    update: (id: string, data: any) => supabase.from('books').update(data).eq('id', id),
    delete: (id: string) => supabase.from('books').delete().eq('id', id),
    search: (query: string) => supabase.from('books').select('*').or(`title.ilike.%${query}%,author.ilike.%${query}%,isbn.ilike.%${query}%`),
    getAvailable: () => supabase.from('books').select('*').gt('available_copies', 0),
    getByCategory: (category: string) => supabase.from('books').select('*').eq('category', category),
  },

  bookTransactions: {
    getAll: () => supabase.from('book_transactions').select('*'),
    getById: (id: string) => supabase.from('book_transactions').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('book_transactions').insert(data),
    update: (id: string, data: any) => supabase.from('book_transactions').update(data).eq('id', id),
    delete: (id: string) => supabase.from('book_transactions').delete().eq('id', id),
    getByUser: (userId: string) => supabase.from('book_transactions').select('*, books(*)').eq('user_id', userId),
    getActive: () => supabase.from('book_transactions').select('*, books(*)').eq('status', 'active'),
    getOverdue: () => supabase.from('book_transactions').select('*, books(*)').eq('status', 'overdue'),
    getUserBorrowedBooks: (userId: string) => supabase.from('book_transactions').select('*, books(*)').eq('user_id', userId).eq('status', 'active'),
  },
};

// Auth helper functions
export const auth = {
  signIn: (email: string, password: string) => supabase.auth.signInWithPassword({ email, password }),
  signOut: () => supabase.auth.signOut(),
  getUser: () => supabase.auth.getUser(),
  getSession: () => supabase.auth.getSession(),
};
