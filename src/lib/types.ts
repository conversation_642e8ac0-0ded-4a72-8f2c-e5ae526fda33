// Database Types
export interface User {
  id: string;
  email: string;
  role: 'admin' | 'teacher' | 'student' | 'parent';
  created_at: string;
  updated_at: string;
}

export interface Student {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  address: string;
  phone: string;
  parent_email: string;
  class_id: string;
  admission_date: string;
  student_id: string;
  status: 'active' | 'inactive' | 'graduated';
  created_at: string;
  updated_at: string;
}

export interface Teacher {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  qualification: string;
  experience_years: number;
  salary: number;
  hire_date: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Class {
  id: string;
  name: string;
  section: string;
  grade: number;
  teacher_id: string;
  academic_year: string;
  max_students: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Subject {
  id: string;
  name: string;
  code: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface Attendance {
  id: string;
  student_id: string;
  class_id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  marked_by: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Timetable {
  id: string;
  class_id: string;
  subject_id: string;
  teacher_id: string;
  day_of_week: number; // 0-6 (Sunday-Saturday)
  start_time: string;
  end_time: string;
  room_number?: string;
  created_at: string;
  updated_at: string;
}

export interface Fee {
  id: string;
  student_id: string;
  fee_type: 'tuition' | 'transport' | 'library' | 'lab' | 'other';
  amount: number;
  due_date: string;
  paid_date?: string;
  status: 'pending' | 'paid' | 'overdue';
  payment_method?: 'cash' | 'card' | 'bank_transfer' | 'online';
  receipt_number?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  target_audience: 'all' | 'teachers' | 'students' | 'parents';
  sent_by: string;
  sent_at: string;
  read_by: string[]; // Array of user IDs who have read the notification
  created_at: string;
  updated_at: string;
}

// Form Types
export interface StudentFormData {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  address: string;
  phone: string;
  parent_email: string;
  class_id: string;
  student_id: string;
}

export interface TeacherFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  qualification: string;
  experience_years: number;
  salary: number;
  hire_date: string;
}

export interface ClassFormData {
  name: string;
  section: string;
  grade: number;
  teacher_id: string;
  academic_year: string;
  max_students: number;
}

// Leave Management Types
export interface LeaveRequest {
  id: string;
  user_id: string;
  user_type: 'teacher' | 'student';
  leave_type: 'sick' | 'personal' | 'emergency' | 'vacation' | 'medical' | 'family';
  start_date: string;
  end_date: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approved_by?: string;
  approval_date?: string;
  rejection_reason?: string;
  documents?: string[]; // URLs to uploaded documents
  created_at: string;
  updated_at: string;
}

export interface LeaveFormData {
  leave_type: 'sick' | 'personal' | 'emergency' | 'vacation' | 'medical' | 'family';
  start_date: string;
  end_date: string;
  reason: string;
  documents?: File[];
}

// Exam & Grade Management Types
export interface Exam {
  id: string;
  name: string;
  subject_id: string;
  class_id: string;
  exam_date: string;
  start_time: string;
  end_time: string;
  total_marks: number;
  passing_marks: number;
  exam_type: 'unit_test' | 'mid_term' | 'final' | 'practical' | 'assignment';
  instructions?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Grade {
  id: string;
  exam_id: string;
  student_id: string;
  marks_obtained: number;
  grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F';
  remarks?: string;
  graded_by: string;
  graded_at: string;
  created_at: string;
  updated_at: string;
}

export interface ExamFormData {
  name: string;
  subject_id: string;
  class_id: string;
  exam_date: string;
  start_time: string;
  end_time: string;
  total_marks: number;
  passing_marks: number;
  exam_type: 'unit_test' | 'mid_term' | 'final' | 'practical' | 'assignment';
  instructions?: string;
}

// Event & Announcement Types
export interface Event {
  id: string;
  title: string;
  description: string;
  event_type: 'academic' | 'sports' | 'cultural' | 'holiday' | 'meeting' | 'exam' | 'other';
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  target_audience: 'all' | 'students' | 'teachers' | 'parents' | 'staff';
  is_holiday: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  target_audience: 'all' | 'students' | 'teachers' | 'parents' | 'staff' | 'class_specific';
  class_ids?: string[]; // For class-specific announcements
  is_emergency: boolean;
  expires_at?: string;
  attachments?: string[];
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface EventFormData {
  title: string;
  description: string;
  event_type: 'academic' | 'sports' | 'cultural' | 'holiday' | 'meeting' | 'exam' | 'other';
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  target_audience: 'all' | 'students' | 'teachers' | 'parents' | 'staff';
  is_holiday: boolean;
}

// Library Management Types
export interface Book {
  id: string;
  title: string;
  author: string;
  isbn: string;
  category: string;
  publisher: string;
  publication_year: number;
  total_copies: number;
  available_copies: number;
  location: string; // Shelf/Section
  description?: string;
  cover_image?: string;
  created_at: string;
  updated_at: string;
}

export interface BookTransaction {
  id: string;
  book_id: string;
  user_id: string;
  user_type: 'student' | 'teacher';
  transaction_type: 'borrow' | 'return';
  transaction_date: string;
  due_date?: string;
  return_date?: string;
  fine_amount?: number;
  status: 'active' | 'returned' | 'overdue' | 'lost';
  notes?: string;
  processed_by: string;
  created_at: string;
  updated_at: string;
}

export interface BookFormData {
  title: string;
  author: string;
  isbn: string;
  category: string;
  publisher: string;
  publication_year: number;
  total_copies: number;
  location: string;
  description?: string;
}

// Transport Management Types
export interface Bus {
  id: string;
  bus_number: string;
  driver_name: string;
  driver_phone: string;
  capacity: number;
  route_name: string;
  status: 'active' | 'maintenance' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface BusRoute {
  id: string;
  route_name: string;
  bus_id: string;
  stops: BusStop[];
  start_time: string;
  end_time: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface BusStop {
  id: string;
  stop_name: string;
  address: string;
  arrival_time: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface StudentTransport {
  id: string;
  student_id: string;
  bus_route_id: string;
  pickup_stop_id: string;
  drop_stop_id: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// Messaging System Types
export interface Message {
  id: string;
  sender_id: string;
  sender_type: 'admin' | 'teacher' | 'student' | 'parent';
  recipient_id: string;
  recipient_type: 'admin' | 'teacher' | 'student' | 'parent';
  subject: string;
  content: string;
  message_type: 'direct' | 'announcement' | 'notification';
  priority: 'low' | 'medium' | 'high';
  is_read: boolean;
  read_at?: string;
  attachments?: string[];
  parent_message_id?: string; // For replies
  created_at: string;
  updated_at: string;
}

export interface MessageFormData {
  recipient_id: string;
  recipient_type: 'admin' | 'teacher' | 'student' | 'parent';
  subject: string;
  content: string;
  priority: 'low' | 'medium' | 'high';
  attachments?: File[];
}

// Enhanced Fee Management Types
export interface FeeStructure {
  id: string;
  class_id: string;
  academic_year: string;
  tuition_fee: number;
  library_fee: number;
  lab_fee: number;
  sports_fee: number;
  transport_fee: number;
  other_fees: { [key: string]: number };
  total_annual_fee: number;
  installment_plan: InstallmentPlan[];
  discount_rules: DiscountRule[];
  created_at: string;
  updated_at: string;
}

export interface InstallmentPlan {
  id: string;
  installment_number: number;
  due_date: string;
  amount: number;
  description: string;
}

export interface DiscountRule {
  id: string;
  rule_name: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  applicable_to: 'all' | 'siblings' | 'merit' | 'need_based';
  conditions: string;
}

export interface FeePayment {
  id: string;
  student_id: string;
  fee_structure_id: string;
  installment_id?: string;
  amount_paid: number;
  payment_method: 'cash' | 'card' | 'bank_transfer' | 'online' | 'cheque';
  payment_date: string;
  transaction_id?: string;
  receipt_number: string;
  discount_applied?: number;
  late_fee?: number;
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  processed_by: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Role-Based Access Control Types
export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  is_system_role: boolean;
  created_at: string;
  updated_at: string;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'manage';
  description: string;
}

export interface UserRole {
  id: string;
  user_id: string;
  role_id: string;
  assigned_by: string;
  assigned_at: string;
  expires_at?: string;
  is_active: boolean;
}

// Analytics Types
export interface AnalyticsData {
  attendance: {
    overall_percentage: number;
    class_wise: { class_id: string; percentage: number }[];
    monthly_trend: { month: string; percentage: number }[];
  };
  academic: {
    grade_distribution: { grade: string; count: number }[];
    subject_performance: { subject_id: string; average_marks: number }[];
    class_performance: { class_id: string; average_marks: number }[];
  };
  financial: {
    total_collected: number;
    pending_amount: number;
    monthly_collection: { month: string; amount: number }[];
    payment_method_breakdown: { method: string; amount: number }[];
  };
  enrollment: {
    total_students: number;
    new_admissions: number;
    dropouts: number;
    monthly_trend: { month: string; admissions: number; dropouts: number }[];
  };
}

// Dashboard Types
export interface DashboardStats {
  total_students: number;
  total_teachers: number;
  total_classes: number;
  attendance_rate: number;
  pending_fees: number;
  recent_notifications: number;
}

export interface AttendanceReport {
  date: string;
  present: number;
  absent: number;
  late: number;
  total: number;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
